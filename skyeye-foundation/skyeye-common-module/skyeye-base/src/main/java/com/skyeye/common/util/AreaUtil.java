/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.common.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.skyeye.common.constans.CommonConstants;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: AreaUtil
 * @Description: 初始化地区部
 * @author: skyeye云系列--卫志强
 * @date: 2021/7/6 22:03
 * @Copyright: 2021 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
public class AreaUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(AreaUtil.class);

    /**
     * 获取地区api
     */
    private static final String URL_JD_AREA = "https://fts.jd.com/area/get?fid=%d";

    /**
     * 初始化省份数据
     */
    private static final String[] TABLE_PROVINCE = new String[]{"1", "北京", "2", "上海", "3", "天津", "4", "重庆", "5", "河北",
        "6", "山西", "7", "河南", "8", "辽宁", "9", "吉林", "10", "黑龙江", "11", "内蒙古", "12", "江苏", "13", "山东", "14", "安徽",
        "15", "浙江", "16", "福建", "17", "湖北", "18", "湖南", "19", "广东", "20", "广西", "21", "江西", "22", "四川", "23", "海南",
        "24", "贵州", "25", "云南", "26", "西藏", "27", "陕西", "28", "甘肃", "29", "青海", "30", "宁夏", "31", "新疆", "32", "台湾",
        "42", "香港", "43", "澳门", "84", "钓鱼岛"};

    private static final String USER_ID = CommonConstants.ADMIN_USER_ID;

    private static final String TIME = DateUtil.getTimeAndToString();

    /**
     * 初始化省份数据
     * 从Spring配置文件中读取数据库连接信息
     */
    public static void initArea() {
        try {
            LOGGER.info("开始初始化地区数据...");

            // 从Spring配置文件中读取数据库连接信息
            String datasourceUrl = PropertiesUtil.getPropertiesValue("${spring.datasource.url}");
            String username = PropertiesUtil.getPropertiesValue("${spring.datasource.username}");
            String password = PropertiesUtil.getPropertiesValue("${spring.datasource.password}");

            if (StrUtil.isBlank(datasourceUrl) || StrUtil.isBlank(username) || StrUtil.isBlank(password)) {
                LOGGER.error("数据库连接配置信息不完整，请检查spring.datasource配置");
                throw new RuntimeException("数据库连接配置信息不完整");
            }

            // 解析数据库连接URL获取主机、端口、数据库名
            DatabaseConnectionInfo dbInfo = parseDatasourceUrl(datasourceUrl);
            LOGGER.info("使用数据库连接: {}:{}/{}", dbInfo.getHost(), dbInfo.getPort(), dbInfo.getDatabase());

            Connection conn = getConn(dbInfo.getHost(), dbInfo.getPort(), dbInfo.getDatabase(), username, password);
            Map<Integer, String> dataMap = getProvinceMap(conn);

            LOGGER.info("开始处理省份数据，共{}个省份", TABLE_PROVINCE.length / 2);
            for (int nIndex = 0; nIndex < TABLE_PROVINCE.length; nIndex = nIndex + 2) {
                int id = Integer.parseInt(TABLE_PROVINCE[nIndex]);
                String name = TABLE_PROVINCE[nIndex + 1];
                if (StrUtil.isNotEmpty(dataMap.get(id))) {
                    updateArea(conn, dataMap.get(id), id, name, 0, 0);
                } else {
                    insertArea(conn, ToolUtil.getSurFaceId(), id, name, 0, 0);
                }
                initChildArea(conn, id, 1, dataMap);
            }
            conn.close();
            LOGGER.info("地区数据初始化完成");
        } catch (Exception e) {
            LOGGER.error("初始化地区数据失败", e);
            throw new RuntimeException("初始化地区数据失败", e);
        }
    }

    /**
     * 获取各省下级地区
     *
     * @param conn     数据库连接对象
     * @param parentId 所属地区ID
     * @param level    地区层级，省级：0，市级：1，...
     */
    public static void initChildArea(Connection conn, int parentId, int level, Map<Integer, String> dataMap) {
        String url = String.format(URL_JD_AREA, parentId);
        String text = HttpClient.doGet(url);
        if (!StringUtils.isEmpty(text)) {
            List<Map<String, Object>> array = JSONUtil.toList(text, null);
            if (array != null && array.size() > 0) {
                for (int nIndex = 0; nIndex < array.size(); nIndex++) {
                    Map<String, Object> object = array.get(nIndex);
                    int id = Integer.parseInt(object.get("id").toString());
                    String name = object.get("name").toString();
                    if (StrUtil.isNotEmpty(dataMap.get(id))) {
                        updateArea(conn, dataMap.get(id), id, name, parentId, level);
                    } else {
                        insertArea(conn, ToolUtil.getSurFaceId(), id, name, parentId, level);
                    }
                    initChildArea(conn, id, level + 1, dataMap);
                }
            }
        }
    }

    public static void insertArea(Connection conn, String dataId, int codeId, String name, int parentId, int level) {
        try {
            Statement stat = conn.createStatement();
            String sql = "INSERT INTO t_area VALUES ('" + dataId + "', " + codeId + ", '" + name + "', " + parentId + ", " + level
                + ", '" + USER_ID + "', '" + TIME + "', '" + USER_ID + "', '" + TIME + "')";
            stat.execute(sql);
            stat.close();
            System.out.println("新增：" + name + "--级别：" + level);
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    public static void updateArea(Connection conn, String dataId, int codeId, String name, int parentId, int level) {
        try {
            Statement stat = conn.createStatement();
            String sql = "UPDATE t_area SET code_id = " + codeId + ", name = '" + name + "', parent_code_id = " + parentId + ", level = " + level
                + ", last_update_id = '" + USER_ID + "', last_update_time = '" + TIME + "' WHERE id = '" + dataId + "'";
            stat.execute(sql);
            stat.close();
            System.out.println("更新：" + name + "--级别：" + level);
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    public static Map<Integer, String> getProvinceMap(Connection conn) {
        try {
            Statement stat = conn.createStatement();
            String sql = "SELECT id, code_id FROM t_area";
            ResultSet resultSet = stat.executeQuery(sql);
            Map<Integer, String> map = new java.util.HashMap<>();
            while (resultSet.next()) {
                map.put(resultSet.getInt("code_id"), resultSet.getString("id"));
            }
            return map;
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 解析数据库连接URL
     *
     * @param datasourceUrl 数据库连接URL，格式如：***********************************?...
     * @return 数据库连接信息
     */
    private static DatabaseConnectionInfo parseDatasourceUrl(String datasourceUrl) {
        try {
            // 示例URL: **************************************************************************...
            String urlWithoutPrefix = datasourceUrl.substring("jdbc:mysql://".length());
            String[] parts = urlWithoutPrefix.split("\\?")[0].split("/");
            String hostPort = parts[0];
            String database = parts[1];

            String[] hostPortParts = hostPort.split(":");
            String host = hostPortParts[0];
            String port = hostPortParts.length > 1 ? hostPortParts[1] : "3306";

            return new DatabaseConnectionInfo(host, port, database);
        } catch (Exception e) {
            LOGGER.error("解析数据库连接URL失败: {}", datasourceUrl, e);
            throw new RuntimeException("解析数据库连接URL失败", e);
        }
    }

    /**
     * 数据库连接信息内部类
     */
    private static class DatabaseConnectionInfo {
        private final String host;
        private final String port;
        private final String database;

        public DatabaseConnectionInfo(String host, String port, String database) {
            this.host = host;
            this.port = port;
            this.database = database;
        }

        public String getHost() {
            return host;
        }

        public String getPort() {
            return port;
        }

        public String getDatabase() {
            return database;
        }
    }

    /**
     * 链接数据库
     *
     * @param dbHost     数据库主机地址
     * @param dbPort     数据库端口
     * @param dbName     数据库名称
     * @param dbUser     数据库用户名称
     * @param dbPassword 数据库登录密码
     * @return
     * @throws Exception
     */
    public static Connection getConn(String dbHost, String dbPort, String dbName, String dbUser, String dbPassword) throws Exception {
        // 使用新版本的MySQL驱动
        Class.forName("com.mysql.cj.jdbc.Driver");
        String connStr = "jdbc:mysql://" + dbHost + ":" + dbPort + "/" + dbName
            + "?user=" + dbUser + "&password=" + dbPassword
            + "&useUnicode=true&characterEncoding=utf8&allowMultiQueries=true"
            + "&useSSL=false&rewriteBatchedStatements=true&serverTimezone=GMT%2B8"
            + "&allowPublicKeyRetrieval=true";

        LOGGER.debug("数据库连接字符串: {}", connStr.replaceAll("password=[^&]*", "password=***"));
        Connection conn = DriverManager.getConnection(connStr);
        return conn;
    }

    /**
     * 主方法，用于独立运行地区数据初始化
     * 注意：需要在Spring容器环境中运行，以便读取配置文件
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        try {
            System.out.println("=== Skyeye地区数据初始化工具 ===");
            System.out.println("注意：此工具需要在Spring容器环境中运行");
            System.out.println("请确保已正确配置spring.datasource相关参数");
            System.out.println("开始初始化地区数据...");

            initArea();

            System.out.println("地区数据初始化完成！");
        } catch (Exception e) {
            System.err.println("地区数据初始化失败: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }

}
