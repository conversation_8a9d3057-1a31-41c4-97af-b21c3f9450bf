Z:\jdk-17.0.15.6-hotspot\bin\java.exe --add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.lang.reflect=ALL-UNNAMED --add-opens java.base/java.lang.invoke=ALL-UNNAMED --add-opens java.base/java.math=ALL-UNNAMED --add-opens java.base/sun.net.util=ALL-UNNAMED --add-opens java.base/java.io=ALL-UNNAMED --add-opens java.base/java.net=ALL-UNNAMED --add-opens java.base/java.nio=ALL-UNNAMED --add-opens java.base/java.security=ALL-UNNAMED --add-opens java.base/java.text=ALL-UNNAMED --add-opens java.base/java.time=ALL-UNNAMED --add-opens java.base/java.util=ALL-UNNAMED -XX:TieredStopAtLevel=1 -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true "-Dmanagement.endpoints.jmx.exposure.include=*" "-javaagent:Z:\JetBrains\IntelliJ IDEA 2025.1\lib\idea_rt.jar=55898" -Dfile.encoding=UTF-8 -classpath Z:\IdeaProjects\erp\skyeye-business\skyeye-crm\crm-web\target\classes;Z:\IdeaProjects\erp\skyeye-business\skyeye-crm\crm-pro\target\classes;Z:\IdeaProjects\erp\skyeye-foundation\skyeye-common-rest\target\classes;Z:\IdeaProjects\erp\skyeye-foundation\skyeye-common-module\skyeye-base\target\classes;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\2.7.18\spring-boot-starter-web-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\2.7.18\spring-boot-starter-json-2.7.18.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.13.5\jackson-datatype-jdk8-2.13.5.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.13.5\jackson-datatype-jsr310-2.13.5.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.13.5\jackson-module-parameter-names-2.13.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\2.7.18\spring-boot-starter-tomcat-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.83\tomcat-embed-core-9.0.83.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\9.0.83\tomcat-embed-el-9.0.83.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.83\tomcat-embed-websocket-9.0.83.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\5.3.31\spring-web-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\5.3.31\spring-beans-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\5.3.31\spring-webmvc-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\5.3.31\spring-context-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\5.3.31\spring-expression-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-actuator\2.7.18\spring-boot-starter-actuator-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator-autoconfigure\2.7.18\spring-boot-actuator-autoconfigure-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator\2.7.18\spring-boot-actuator-2.7.18.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-core\1.9.17\micrometer-core-1.9.17.jar;C:\Users\<USER>\.m2\repository\org\hdrhistogram\HdrHistogram\2.1.12\HdrHistogram-2.1.12.jar;C:\Users\<USER>\.m2\repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;C:\Users\<USER>\.m2\repository\org\jolokia\jolokia-core\1.7.2\jolokia-core-1.7.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\2.7.18\spring-boot-starter-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\2.7.18\spring-boot-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\2.7.18\spring-boot-autoconfigure-2.7.18.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\5.3.31\spring-core-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\5.3.31\spring-jcl-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\1.30\snakeyaml-1.30.jar;C:\Users\<USER>\.m2\repository\org\apache\rocketmq\rocketmq-spring-boot-starter\2.2.1\rocketmq-spring-boot-starter-2.2.1.jar;C:\Users\<USER>\.m2\repository\org\apache\rocketmq\rocketmq-spring-boot\2.2.1\rocketmq-spring-boot-2.2.1.jar;C:\Users\<USER>\.m2\repository\org\apache\rocketmq\rocketmq-client\4.9.1\rocketmq-client-4.9.1.jar;C:\Users\<USER>\.m2\repository\org\apache\rocketmq\rocketmq-common\4.9.1\rocketmq-common-4.9.1.jar;C:\Users\<USER>\.m2\repository\org\apache\rocketmq\rocketmq-acl\4.9.1\rocketmq-acl-4.9.1.jar;C:\Users\<USER>\.m2\repository\org\apache\rocketmq\rocketmq-remoting\4.9.1\rocketmq-remoting-4.9.1.jar;C:\Users\<USER>\.m2\repository\org\apache\rocketmq\rocketmq-logging\4.9.1\rocketmq-logging-4.9.1.jar;C:\Users\<USER>\.m2\repository\org\apache\rocketmq\rocketmq-srvutil\4.9.1\rocketmq-srvutil-4.9.1.jar;C:\Users\<USER>\.m2\repository\commons-cli\commons-cli\1.2\commons-cli-1.2.jar;C:\Users\<USER>\.m2\repository\commons-validator\commons-validator\1.7\commons-validator-1.7.jar;C:\Users\<USER>\.m2\repository\commons-digester\commons-digester\2.1\commons-digester-2.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-validation\2.7.18\spring-boot-starter-validation-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\hibernate\validator\hibernate-validator\6.2.5.Final\hibernate-validator-6.2.5.Final.jar;C:\Users\<USER>\.m2\repository\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar;C:\Users\<USER>\.m2\repository\org\jboss\logging\jboss-logging\3.4.3.Final\jboss-logging-3.4.3.Final.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;C:\Users\<USER>\.m2\repository\com\alibaba\cloud\spring-cloud-starter-alibaba-nacos-config\2021.0.6.1\spring-cloud-starter-alibaba-nacos-config-2021.0.6.1.jar;C:\Users\<USER>\.m2\repository\com\alibaba\cloud\spring-cloud-alibaba-commons\2021.0.6.1\spring-cloud-alibaba-commons-2021.0.6.1.jar;C:\Users\<USER>\.m2\repository\com\alibaba\spring\spring-context-support\1.0.11\spring-context-support-1.0.11.jar;C:\Users\<USER>\.m2\repository\com\alibaba\nacos\nacos-client\2.2.0\nacos-client-2.2.0.jar;C:\Users\<USER>\.m2\repository\com\alibaba\nacos\nacos-auth-plugin\2.2.0\nacos-auth-plugin-2.2.0.jar;C:\Users\<USER>\.m2\repository\com\alibaba\nacos\nacos-encryption-plugin\2.2.0\nacos-encryption-plugin-2.2.0.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpasyncclient\4.1.5\httpasyncclient-4.1.5.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore-nio\4.4.16\httpcore-nio-4.4.16.jar;C:\Users\<USER>\.m2\repository\io\prometheus\simpleclient\0.15.0\simpleclient-0.15.0.jar;C:\Users\<USER>\.m2\repository\io\prometheus\simpleclient_tracer_otel\0.15.0\simpleclient_tracer_otel-0.15.0.jar;C:\Users\<USER>\.m2\repository\io\prometheus\simpleclient_tracer_common\0.15.0\simpleclient_tracer_common-0.15.0.jar;C:\Users\<USER>\.m2\repository\io\prometheus\simpleclient_tracer_otel_agent\0.15.0\simpleclient_tracer_otel_agent-0.15.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-commons\3.1.8\spring-cloud-commons-3.1.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-crypto\5.7.11\spring-security-crypto-5.7.11.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-context\3.1.8\spring-cloud-context-3.1.8.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;C:\Users\<USER>\.m2\repository\com\alibaba\cloud\spring-cloud-starter-alibaba-nacos-discovery\2021.0.6.1\spring-cloud-starter-alibaba-nacos-discovery-2021.0.6.1.jar;C:\Users\<USER>\.m2\repository\com\alibaba\cloud\spring-cloud-starter-alibaba-sentinel\2021.0.6.1\spring-cloud-starter-alibaba-sentinel-2021.0.6.1.jar;C:\Users\<USER>\.m2\repository\com\alibaba\csp\sentinel-transport-simple-http\1.8.6\sentinel-transport-simple-http-1.8.6.jar;C:\Users\<USER>\.m2\repository\com\alibaba\csp\sentinel-transport-common\1.8.6\sentinel-transport-common-1.8.6.jar;C:\Users\<USER>\.m2\repository\com\alibaba\csp\sentinel-datasource-extension\1.8.6\sentinel-datasource-extension-1.8.6.jar;C:\Users\<USER>\.m2\repository\com\alibaba\csp\sentinel-annotation-aspectj\1.8.6\sentinel-annotation-aspectj-1.8.6.jar;C:\Users\<USER>\.m2\repository\com\alibaba\csp\sentinel-core\1.8.6\sentinel-core-1.8.6.jar;C:\Users\<USER>\.m2\repository\com\alibaba\cloud\spring-cloud-circuitbreaker-sentinel\2021.0.6.1\spring-cloud-circuitbreaker-sentinel-2021.0.6.1.jar;C:\Users\<USER>\.m2\repository\com\alibaba\csp\sentinel-reactor-adapter\1.8.6\sentinel-reactor-adapter-1.8.6.jar;C:\Users\<USER>\.m2\repository\com\alibaba\csp\sentinel-spring-webflux-adapter\1.8.6\sentinel-spring-webflux-adapter-1.8.6.jar;C:\Users\<USER>\.m2\repository\com\alibaba\csp\sentinel-spring-webmvc-adapter\1.8.6\sentinel-spring-webmvc-adapter-1.8.6.jar;C:\Users\<USER>\.m2\repository\com\alibaba\csp\sentinel-parameter-flow-control\1.8.6\sentinel-parameter-flow-control-1.8.6.jar;C:\Users\<USER>\.m2\repository\com\googlecode\concurrentlinkedhashmap\concurrentlinkedhashmap-lru\1.4.2\concurrentlinkedhashmap-lru-1.4.2.jar;C:\Users\<USER>\.m2\repository\com\alibaba\csp\sentinel-cluster-server-default\1.8.6\sentinel-cluster-server-default-1.8.6.jar;C:\Users\<USER>\.m2\repository\com\alibaba\csp\sentinel-cluster-common-default\1.8.6\sentinel-cluster-common-default-1.8.6.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.101.Final\netty-handler-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\com\alibaba\csp\sentinel-cluster-client-default\1.8.6\sentinel-cluster-client-default-1.8.6.jar;C:\Users\<USER>\.m2\repository\com\alibaba\cloud\spring-cloud-alibaba-sentinel-datasource\2021.0.6.1\spring-cloud-alibaba-sentinel-datasource-2021.0.6.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter-bootstrap\3.1.8\spring-cloud-starter-bootstrap-3.1.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter\3.1.8\spring-cloud-starter-3.1.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-rsa\1.0.12.RELEASE\spring-security-rsa-1.0.12.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcpkix-jdk18on\1.73\bcpkix-jdk18on-1.73.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcutil-jdk18on\1.73\bcutil-jdk18on-1.73.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter-loadbalancer\3.1.8\spring-cloud-starter-loadbalancer-3.1.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-loadbalancer\3.1.8\spring-cloud-loadbalancer-3.1.8.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.4.34\reactor-core-3.4.34.jar;C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\addons\reactor-extra\3.4.10\reactor-extra-3.4.10.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-cache\2.7.18\spring-boot-starter-cache-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context-support\5.3.31\spring-context-support-5.3.31.jar;C:\Users\<USER>\.m2\repository\com\stoyanr\evictor\1.0.0\evictor-1.0.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter-openfeign\3.1.9\spring-cloud-starter-openfeign-3.1.9.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-openfeign-core\3.1.9\spring-cloud-openfeign-core-3.1.9.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\form\feign-form-spring\3.8.0\feign-form-spring-3.8.0.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\form\feign-form\3.8.0\feign-form-3.8.0.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\feign-core\11.10\feign-core-11.10.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\feign-slf4j\11.10\feign-slf4j-11.10.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\feign-okhttp\11.10\feign-okhttp-11.10.jar;C:\Users\<USER>\.m2\repository\com\squareup\okhttp3\okhttp\4.9.3\okhttp-4.9.3.jar;C:\Users\<USER>\.m2\repository\com\squareup\okio\okio\2.8.0\okio-2.8.0.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-common\1.6.21\kotlin-stdlib-common-1.6.21.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib\1.6.21\kotlin-stdlib-1.6.21.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\annotations\13.0\annotations-13.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-websocket\2.7.18\spring-boot-starter-websocket-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-messaging\5.3.31\spring-messaging-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-websocket\5.3.31\spring-websocket-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-aop\2.7.18\spring-boot-starter-aop-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\5.3.31\spring-aop-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.7\aspectjweaver-1.9.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-log4j2\2.7.18\spring-boot-starter-log4j2-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-slf4j-impl\2.17.2\log4j-slf4j-impl-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-jul\2.17.2\log4j-jul-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\1.7.36\jul-to-slf4j-1.7.36.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.17.0\log4j-api-2.17.0.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-core\2.17.0\log4j-core-2.17.0.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-boot-starter\3.5.1\mybatis-plus-boot-starter-3.5.1.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus\3.5.1\mybatis-plus-3.5.1.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-extension\3.5.1\mybatis-plus-extension-3.5.1.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-core\3.5.1\mybatis-plus-core-3.5.1.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-annotation\3.5.1\mybatis-plus-annotation-3.5.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\2.7.18\spring-boot-starter-jdbc-2.7.18.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\5.3.31\spring-jdbc-5.3.31.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-generator\3.5.1\mybatis-plus-generator-3.5.1.jar;C:\Users\<USER>\.m2\repository\com\github\yulichang\mybatis-plus-join-boot-starter\1.4.13\mybatis-plus-join-boot-starter-1.4.13.jar;C:\Users\<USER>\.m2\repository\com\github\yulichang\mybatis-plus-join-extension\1.4.13\mybatis-plus-join-extension-1.4.13.jar;C:\Users\<USER>\.m2\repository\com\github\yulichang\mybatis-plus-join-core\1.4.13\mybatis-plus-join-core-1.4.13.jar;C:\Users\<USER>\.m2\repository\com\github\yulichang\mybatis-plus-join-annotation\1.4.13\mybatis-plus-join-annotation-1.4.13.jar;C:\Users\<USER>\.m2\repository\com\github\yulichang\mybatis-plus-join-adapter-v33x\1.4.13\mybatis-plus-join-adapter-v33x-1.4.13.jar;C:\Users\<USER>\.m2\repository\com\github\yulichang\mybatis-plus-join-adapter-base\1.4.13\mybatis-plus-join-adapter-base-1.4.13.jar;C:\Users\<USER>\.m2\repository\com\github\yulichang\mybatis-plus-join-adapter-jsqlparser\1.4.13\mybatis-plus-join-adapter-jsqlparser-1.4.13.jar;C:\Users\<USER>\.m2\repository\com\github\yulichang\mybatis-plus-join-adapter-jsqlparser-v46\1.4.13\mybatis-plus-join-adapter-jsqlparser-v46-1.4.13.jar;C:\Users\<USER>\.m2\repository\com\github\yulichang\mybatis-plus-join-adapter-v3431\1.4.13\mybatis-plus-join-adapter-v3431-1.4.13.jar;C:\Users\<USER>\.m2\repository\com\github\yulichang\mybatis-plus-join-adapter-v352\1.4.13\mybatis-plus-join-adapter-v352-1.4.13.jar;C:\Users\<USER>\.m2\repository\com\github\yulichang\mybatis-plus-join-adapter-v355\1.4.13\mybatis-plus-join-adapter-v355-1.4.13.jar;C:\Users\<USER>\.m2\repository\mysql\mysql-connector-java\8.0.16\mysql-connector-java-8.0.16.jar;C:\Users\<USER>\.m2\repository\com\github\pagehelper\pagehelper-spring-boot-starter\1.4.2\pagehelper-spring-boot-starter-1.4.2.jar;C:\Users\<USER>\.m2\repository\org\mybatis\spring\boot\mybatis-spring-boot-starter\2.2.2\mybatis-spring-boot-starter-2.2.2.jar;C:\Users\<USER>\.m2\repository\org\mybatis\spring\boot\mybatis-spring-boot-autoconfigure\2.2.2\mybatis-spring-boot-autoconfigure-2.2.2.jar;C:\Users\<USER>\.m2\repository\org\mybatis\mybatis\3.5.9\mybatis-3.5.9.jar;C:\Users\<USER>\.m2\repository\org\mybatis\mybatis-spring\2.0.7\mybatis-spring-2.0.7.jar;C:\Users\<USER>\.m2\repository\com\github\pagehelper\pagehelper-spring-boot-autoconfigure\1.4.2\pagehelper-spring-boot-autoconfigure-1.4.2.jar;C:\Users\<USER>\.m2\repository\com\github\pagehelper\pagehelper\5.3.0\pagehelper-5.3.0.jar;C:\Users\<USER>\.m2\repository\com\github\jsqlparser\jsqlparser\4.2\jsqlparser-4.2.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;C:\Users\<USER>\.m2\repository\org\jsoup\jsoup\1.18.1\jsoup-1.18.1.jar;C:\Users\<USER>\.m2\repository\net\sf\json-lib\json-lib\2.4\json-lib-2.4-jdk15.jar;C:\Users\<USER>\.m2\repository\commons-beanutils\commons-beanutils\1.8.0\commons-beanutils-1.8.0.jar;C:\Users\<USER>\.m2\repository\commons-collections\commons-collections\3.2.1\commons-collections-3.2.1.jar;C:\Users\<USER>\.m2\repository\commons-lang\commons-lang\2.5\commons-lang-2.5.jar;C:\Users\<USER>\.m2\repository\commons-logging\commons-logging\1.1.1\commons-logging-1.1.1.jar;C:\Users\<USER>\.m2\repository\net\sf\ezmorph\ezmorph\1.0.6\ezmorph-1.0.6.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpclient\4.5.14\httpclient-4.5.14.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;C:\Users\<USER>\.m2\repository\org\quartz-scheduler\quartz\2.3.2\quartz-2.3.2.jar;C:\Users\<USER>\.m2\repository\com\mchange\mchange-commons-java\0.2.15\mchange-commons-java-0.2.15.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.30\lombok-1.18.30.jar;C:\Users\<USER>\.m2\repository\javax\persistence\persistence-api\1.0.2\persistence-api-1.0.2.jar;C:\Users\<USER>\.m2\repository\io\minio\minio\8.5.7\minio-8.5.7.jar;C:\Users\<USER>\.m2\repository\com\carrotsearch\thirdparty\simple-xml-safe\2.7.1\simple-xml-safe-2.7.1.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.13.5\jackson-annotations-2.13.5.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.13.5\jackson-core-2.13.5.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.13.5\jackson-databind-2.13.5.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcprov-jdk18on\1.76\bcprov-jdk18on-1.76.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-compress\1.24.0\commons-compress-1.24.0.jar;C:\Users\<USER>\.m2\repository\org\xerial\snappy\snappy-java\********\snappy-java-********.jar;C:\Users\<USER>\.m2\repository\com\alipay\sdk\alipay-sdk-java\4.35.79.ALL\alipay-sdk-java-4.35.79.ALL.jar;C:\Users\<USER>\.m2\repository\com\alibaba\fastjson\1.2.83_noneautotype\fastjson-1.2.83_noneautotype.jar;C:\Users\<USER>\.m2\repository\dom4j\dom4j\1.6.1\dom4j-1.6.1.jar;C:\Users\<USER>\.m2\repository\xml-apis\xml-apis\1.0.b2\xml-apis-1.0.b2.jar;C:\Users\<USER>\.m2\repository\com\xingyuv\spring-boot-starter-justauth\1.0.8\spring-boot-starter-justauth-1.0.8.jar;C:\Users\<USER>\.m2\repository\com\xingyuv\justauth\1.0.8\justauth-1.0.8.jar;C:\Users\<USER>\.m2\repository\com\xingyuv\simple-http\1.0.8\simple-http-1.0.8.jar;C:\Users\<USER>\.m2\repository\joda-time\joda-time\2.10.10\joda-time-2.10.10.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-devtools\2.7.18\spring-boot-devtools-2.7.18.jar;C:\Users\<USER>\.m2\repository\commons-fileupload\commons-fileupload\1.3.1\commons-fileupload-1.3.1.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.2\commons-io-2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-redis\2.7.18\spring-boot-starter-data-redis-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-redis\2.7.18\spring-data-redis-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-keyvalue\2.7.18\spring-data-keyvalue-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\2.7.18\spring-data-commons-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\5.3.31\spring-tx-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-oxm\5.3.31\spring-oxm-5.3.31.jar;C:\Users\<USER>\.m2\repository\io\lettuce\lettuce-core\6.1.10.RELEASE\lettuce-core-6.1.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.101.Final\netty-common-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.101.Final\netty-transport-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\redis\clients\jedis\3.8.0\jedis-3.8.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-pool2\2.11.1\commons-pool2-2.11.1.jar;C:\Users\<USER>\.m2\repository\org\freemarker\freemarker\2.3.32\freemarker-2.3.32.jar;C:\Users\<USER>\.m2\repository\com\sun\mail\javax.mail\1.6.2\javax.mail-1.6.2.jar;C:\Users\<USER>\.m2\repository\javax\activation\activation\1.1\activation-1.1.jar;C:\Users\<USER>\.m2\repository\com\itextpdf\itextpdf\5.5.6\itextpdf-5.5.6.jar;C:\Users\<USER>\.m2\repository\com\itextpdf\itext-asian\5.2.0\itext-asian-5.2.0.jar;C:\Users\<USER>\.m2\repository\com\itextpdf\html2pdf\4.0.5\html2pdf-4.0.5.jar;C:\Users\<USER>\.m2\repository\com\itextpdf\forms\7.2.5\forms-7.2.5.jar;C:\Users\<USER>\.m2\repository\com\itextpdf\svg\7.2.5\svg-7.2.5.jar;C:\Users\<USER>\.m2\repository\com\itextpdf\styled-xml-parser\7.2.5\styled-xml-parser-7.2.5.jar;C:\Users\<USER>\.m2\repository\com\itextpdf\layout\7.2.5\layout-7.2.5.jar;C:\Users\<USER>\.m2\repository\com\itextpdf\io\7.2.5\io-7.2.5.jar;C:\Users\<USER>\.m2\repository\com\itextpdf\commons\7.2.5\commons-7.2.5.jar;C:\Users\<USER>\.m2\repository\com\itextpdf\kernel\7.2.5\kernel-7.2.5.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcpkix-jdk15on\1.70\bcpkix-jdk15on-1.70.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcutil-jdk15on\1.70\bcutil-jdk15on-1.70.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcprov-jdk15on\1.70\bcprov-jdk15on-1.70.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\28.0-jre\guava-28.0-jre.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\2.8.1\checker-qual-2.8.1.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.3.2\error_prone_annotations-2.3.2.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\1.3\j2objc-annotations-1.3.jar;C:\Users\<USER>\.m2\repository\org\codehaus\mojo\animal-sniffer-annotations\1.17\animal-sniffer-annotations-1.17.jar;C:\Users\<USER>\.m2\repository\cn\hutool\hutool-all\5.8.39\hutool-all-5.8.39.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi\4.1.2\poi-4.1.2.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-collections4\4.4\commons-collections4-4.4.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-math3\3.6.1\commons-math3-3.6.1.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\SparseBitSet\1.2\SparseBitSet-1.2.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml-schemas\4.1.2\poi-ooxml-schemas-4.1.2.jar;C:\Users\<USER>\.m2\repository\org\apache\xmlbeans\xmlbeans\3.1.0\xmlbeans-3.1.0.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml\4.1.2\poi-ooxml-4.1.2.jar;C:\Users\<USER>\.m2\repository\com\github\virtuald\curvesapi\1.06\curvesapi-1.06.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-scratchpad\4.1.2\poi-scratchpad-4.1.2.jar;C:\Users\<USER>\.m2\repository\fr\opensagres\xdocreport\fr.opensagres.poi.xwpf.converter.xhtml\2.0.2\fr.opensagres.poi.xwpf.converter.xhtml-2.0.2.jar;C:\Users\<USER>\.m2\repository\fr\opensagres\xdocreport\fr.opensagres.poi.xwpf.converter.core\2.0.2\fr.opensagres.poi.xwpf.converter.core-2.0.2.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\ooxml-schemas\1.4\ooxml-schemas-1.4.jar;C:\Users\<USER>\.m2\repository\fr\opensagres\xdocreport\fr.opensagres.xdocreport.core\2.0.2\fr.opensagres.xdocreport.core-2.0.2.jar;C:\Users\<USER>\.m2\repository\com\auth0\java-jwt\3.10.3\java-jwt-3.10.3.jar;C:\Users\<USER>\.m2\repository\com\google\zxing\core\3.3.3\core-3.3.3.jar;C:\Users\<USER>\.m2\repository\com\google\zxing\javase\3.3.3\javase-3.3.3.jar;C:\Users\<USER>\.m2\repository\com\beust\jcommander\1.72\jcommander-1.72.jar;C:\Users\<USER>\.m2\repository\com\github\jai-imageio\jai-imageio-core\1.4.0\jai-imageio-core-1.4.0.jar;C:\Users\<USER>\.m2\repository\com\belerweb\pinyin4j\2.5.0\pinyin4j-2.5.0.jar;C:\Users\<USER>\.m2\repository\com\google\protobuf\protobuf-java\3.4.0\protobuf-java-3.4.0.jar;C:\Users\<USER>\.m2\repository\cn\afterturn\easypoi-base\4.2.0\easypoi-base-4.2.0.jar;C:\Users\<USER>\.m2\repository\ognl\ognl\3.2.6\ognl-3.2.6.jar;C:\Users\<USER>\.m2\repository\org\javassist\javassist\3.20.0-GA\javassist-3.20.0-GA.jar;C:\Users\<USER>\.m2\repository\javax\validation\validation-api\2.0.1.Final\validation-api-2.0.1.Final.jar;C:\Users\<USER>\.m2\repository\cn\afterturn\easypoi-annotation\4.2.0\easypoi-annotation-4.2.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-jexl\2.1.1\commons-jexl-2.1.1.jar;C:\Users\<USER>\.m2\repository\org\dom4j\dom4j\2.1.1\dom4j-2.1.1.jar;C:\Users\<USER>\.m2\repository\org\openoffice\juh\4.1.2\juh-4.1.2.jar;C:\Users\<USER>\.m2\repository\org\openoffice\jurt\4.1.2\jurt-4.1.2.jar;C:\Users\<USER>\.m2\repository\org\openoffice\ridl\4.1.2\ridl-4.1.2.jar;C:\Users\<USER>\.m2\repository\org\openoffice\unoil\4.1.2\unoil-4.1.2.jar;C:\Users\<USER>\.m2\repository\com\thoughtworks\xstream\xstream\1.4.10\xstream-1.4.10.jar;C:\Users\<USER>\.m2\repository\xmlpull\xmlpull\1.1.3.1\xmlpull-1.1.3.1.jar;C:\Users\<USER>\.m2\repository\xpp3\xpp3_min\1.1.4c\xpp3_min-1.1.4c.jar;C:\Users\<USER>\.m2\repository\com\artofsolving\jodconverter\2.2.1\jodconverter-2.2.1.jar;Z:\IdeaProjects\erp\skyeye-infrastructure\xxl-job-2.3.0\xxl-job-core\target\classes;C:\Users\<USER>\.m2\repository\io\netty\netty-all\4.1.101.Final\netty-all-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.101.Final\netty-buffer-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.101.Final\netty-codec-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-dns\4.1.101.Final\netty-codec-dns-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-haproxy\4.1.101.Final\netty-codec-haproxy-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http\4.1.101.Final\netty-codec-http-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http2\4.1.101.Final\netty-codec-http2-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-memcache\4.1.101.Final\netty-codec-memcache-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-mqtt\4.1.101.Final\netty-codec-mqtt-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-redis\4.1.101.Final\netty-codec-redis-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-smtp\4.1.101.Final\netty-codec-smtp-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-socks\4.1.101.Final\netty-codec-socks-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-stomp\4.1.101.Final\netty-codec-stomp-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-xml\4.1.101.Final\netty-codec-xml-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-unix-common\4.1.101.Final\netty-transport-native-unix-common-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler-proxy\4.1.101.Final\netty-handler-proxy-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler-ssl-ocsp\4.1.101.Final\netty-handler-ssl-ocsp-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.101.Final\netty-resolver-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns\4.1.101.Final\netty-resolver-dns-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-rxtx\4.1.101.Final\netty-transport-rxtx-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-sctp\4.1.101.Final\netty-transport-sctp-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-udt\4.1.101.Final\netty-transport-udt-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-classes-epoll\4.1.101.Final\netty-transport-classes-epoll-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-classes-kqueue\4.1.101.Final\netty-transport-classes-kqueue-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-classes-macos\4.1.101.Final\netty-resolver-dns-classes-macos-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-epoll\4.1.101.Final\netty-transport-native-epoll-4.1.101.Final-linux-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-epoll\4.1.101.Final\netty-transport-native-epoll-4.1.101.Final-linux-aarch_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-kqueue\4.1.101.Final\netty-transport-native-kqueue-4.1.101.Final-osx-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-kqueue\4.1.101.Final\netty-transport-native-kqueue-4.1.101.Final-osx-aarch_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-native-macos\4.1.101.Final\netty-resolver-dns-native-macos-4.1.101.Final-osx-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-native-macos\4.1.101.Final\netty-resolver-dns-native-macos-4.1.101.Final-osx-aarch_64.jar;C:\Users\<USER>\.m2\repository\com\google\code\gson\gson\2.9.1\gson-2.9.1.jar;C:\Users\<USER>\.m2\repository\org\codehaus\groovy\groovy\3.0.19\groovy-3.0.19.jar;C:\Users\<USER>\.m2\repository\com\googlecode\json-simple\json-simple\1.1.1\json-simple-1.1.1.jar;C:\Users\<USER>\.m2\repository\de\codecentric\spring-boot-admin-starter-client\2.7.15\spring-boot-admin-starter-client-2.7.15.jar;C:\Users\<USER>\.m2\repository\de\codecentric\spring-boot-admin-client\2.7.15\spring-boot-admin-client-2.7.15.jar com.CrmApplication
2025-07-30 19:05:32.926  INFO 442152 --- [           main] c.a.n.c.e.SearchableProperties           : properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
/**
 *                             _ooOoo_
 *                            o8888888o
 *                            88" . "88
 *                            (| -_- |)
 *                            O\  =  /O
 *                         ____/`---'\____
 *                       .'  \\|     |//  `.
 *                      /  \\|||  :  |||//  \
 *                     /  _||||| -:- |||||-  \
 *                     |   | \\\  -  /// |   |
 *                     | \_|  ''\---/''  |   |
 *                     \  .-\__  `-`  ___/-. /
 *                   ___`. .'  /--.--\  `. . __
 *                ."" '<  `.___\_<|>_/___.'  >'"".
 *               | | :  `- \`.;`\ _ /`;.`/ - ` : | |
 *               \  \ `-.   \_ __\ /__ _/   .-` /  /
 *          ======`-.____`-.___\_____/___.-`____.-'======
 *                             `=---='
 *          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
 *                     佛祖保佑        永无BUG
 *            佛曰:
 *                   写字楼里写字间，写字间里程序员；
 *                   程序人员写程序，又拿程序换酒钱。
 *                   酒醒只在网上坐，酒醉还来网下眠；
 *                   酒醉酒醒日复日，网上网下年复年。
 *                   但愿老死电脑间，不愿鞠躬老板前；
 *                   奔驰宝马贵者趣，公交自行程序员。
 *                   别人笑我忒疯癫，我笑自己命太贱；
 *                   不见满街漂亮妹，哪个归得程序员？
 *		:: springboot 2.x
 *      :: Skyeye系列框架，作者：卫志强
 *      :: 作者独家专属，未购买禁止私自使用
 *      启动：nohup java -Xms800m -Xmx800m -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=512m -jar -Dspring.cloud.nacos.discovery.server-addr=localhost:9000 -Dspring.cloud.nacos.config.server-addr=localhost:9000  -Dspring.profiles.active=dev crm-web.jar >> /opt/service/project/nohup-crm.out 2>&1 &
 *      清空日志：cat /dev/null > xxx-web.out
*/
2025-07-30 19:05:34.521  INFO 442152 --- [           main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-30 19:05:34.521  INFO 442152 --- [           main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-30 19:05:35.277  WARN 442152 --- [           main] c.a.c.n.c.NacosPropertySourceBuilder     : Ignore the empty nacos configuration and get it based on dataId[skyeye-crm-dev] & group[DEFAULT_GROUP]
2025-07-30 19:05:35.295  WARN 442152 --- [           main] c.a.c.n.c.NacosPropertySourceBuilder     : Ignore the empty nacos configuration and get it based on dataId[skyeye-crm-dev-dev.yml] & group[DEFAULT_GROUP]
2025-07-30 19:05:35.296  INFO 442152 --- [           main] b.c.PropertySourceBootstrapConfiguration : Located property source: [BootstrapPropertySource {name='bootstrapProperties-skyeye-crm-dev-dev.yml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-skyeye-crm-dev.yml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-skyeye-crm-dev,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-skyeye-common.yml,DEFAULT_GROUP'}]
2025-07-30 19:05:35.326  INFO 442152 --- [           main] c.CrmApplication                         : The following 1 profile is active: "dev"
2025-07-30 19:05:40.082  INFO 442152 --- [           main] o.s.c.c.s.GenericScope                   : BeanFactory id=1e455201-3a3d-3e4f-a2d4-ce165ad1ade4
2025-07-30 19:05:40.527  INFO 442152 --- [           main] o.s.b.w.e.t.TomcatWebServer              : Tomcat initialized with port(s): 8102 (http)
2025-07-30 19:05:40.534  INFO 442152 --- [           main] o.a.c.c.StandardService                  : Starting service [Tomcat]
2025-07-30 19:05:40.534  INFO 442152 --- [           main] o.a.c.c.StandardEngine                   : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-30 19:05:40.661  INFO 442152 --- [           main] o.a.c.c.C.[.[.[/]                        : Initializing Spring embedded WebApplicationContext
2025-07-30 19:05:40.661  INFO 442152 --- [           main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 5326 ms
Loading class `com.mysql.jdbc.Driver'. This is deprecated. The new driver class is `com.mysql.cj.jdbc.Driver'. The driver is automatically registered via the SPI and manual loading of the driver class is generally unnecessary.
2025-07-30 19:05:40.819  INFO 442152 --- [           main] o.s.b.a.e.w.ServletEndpointRegistrar     : Registered '/actuator/jolokia' to jolokia-actuator-endpoint
2025-07-30 19:05:40.868  INFO 442152 --- [           main] c.z.h.HikariDataSource                   : HikariPool-1 - Starting...
2025-07-30 19:05:40.873  WARN 442152 --- [           main] c.z.h.u.DriverDataSource                 : Registered driver with driverClassName=com.mysql.jdbc.Driver was not found, trying direct instantiation.
2025-07-30 19:05:42.464  INFO 442152 --- [           main] c.z.h.HikariDataSource                   : HikariPool-1 - Start completed.
2025-07-30 19:05:42.467  INFO 442152 --- [           main] c.s.d.c.BaseDataSourceConfig             : database product name: 'MySQL'
2025-07-30 19:05:42.467  INFO 442152 --- [           main] c.s.d.c.BaseDataSourceConfig             : using database type: mysql
 _ _   |_  _ _|_. ___ _ |    _
| | |\/|_)(_| | |_\  |_)||_|_\
     /               |
                        3.5.1
2025-07-30 19:05:43.015  INFO 442152 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 19:05:43.066  INFO 442152 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 19:05:43.075  INFO 442152 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 19:05:43.080  INFO 442152 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 19:05:43.085  INFO 442152 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 19:05:43.106  INFO 442152 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-flowable-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 19:05:43.113  INFO 442152 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 19:05:43.122  INFO 442152 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 19:05:43.129  INFO 442152 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 19:05:43.251  INFO 442152 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-erp-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 19:05:43.258  INFO 442152 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-erp-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 19:05:43.383  INFO 442152 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 19:05:43.976  INFO 442152 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 19:05:44.085  INFO 442152 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-crm-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 19:05:44.091  INFO 442152 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-crm-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 19:05:44.098  INFO 442152 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-erp-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 19:05:44.104  INFO 442152 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-erp-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 19:05:44.110  INFO 442152 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 19:05:44.118  INFO 442152 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 19:05:44.123  INFO 442152 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 19:05:44.139  INFO 442152 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-adm-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 19:05:44.146  INFO 442152 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-adm-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 19:05:44.161  INFO 442152 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-ifs-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 19:05:44.168  INFO 442152 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 19:05:44.174  INFO 442152 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 19:05:44.181  INFO 442152 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 19:05:45.372  INFO 442152 --- [           main] o.s.c.c.u.InetUtils                      : Cannot determine local hostname
2025-07-30 19:05:58.453  INFO 442152 --- [           main] o.s.c.c.u.InetUtils                      : Cannot determine local hostname
2025-07-30 19:05:58.466  INFO 442152 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-shop-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 19:05:58.481  INFO 442152 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-wages-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 19:05:58.488  INFO 442152 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 19:05:58.498  INFO 442152 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 19:05:58.508  WARN 442152 --- [           main] c.s.c.u.IPSeeker                         : load file path config error.
null/util/qqwry.dat
IP地址信息文件没有找到，IP显示功能将无法使用
2025-07-30 19:05:58.508  WARN 442152 --- [           main] c.s.c.u.IPSeeker                         : load file path config error.
null/util/qqwry.dat
IP地址信息文件没有找到，IP显示功能将无法使用
2025-07-30 19:05:58.518  INFO 442152 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 19:05:58.523  INFO 442152 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 19:05:58.529  INFO 442152 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 19:05:58.545  INFO 442152 --- [           main] c.s.c.n.NacosConfigService               : ==========创建configService实例===============
2025-07-30 19:05:58.547  INFO 442152 --- [           main] c.s.c.x.XxlJobConfig                     : >>>>>>>>>>> xxl-job config init.
2025-07-30 19:05:58.980  INFO 442152 --- [           main] c.a.c.s.SentinelWebMvcConfigurer         : [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-30 19:05:59.295  INFO 442152 --- [           main] c.g.y.a.MybatisPlusJoinAutoConfiguration : mybatis plus join properties config complete
 _ _   |_  _ _|_. ___ _ |    _  .  _  .  _
| | |\/|_)(_| | |_\  |_)||_|_\  | (_) | | |
     /               |          /
                                    1.4.13
2025-07-30 19:05:59.346  INFO 442152 --- [           main] c.g.y.a.MybatisPlusJoinAutoConfiguration : mybatis plus join SqlInjector init
2025-07-30 19:05:59.447  INFO 442152 --- [           main] o.q.i.StdSchedulerFactory                : Using default implementation for ThreadExecutor
2025-07-30 19:05:59.453  INFO 442152 --- [           main] o.q.c.SchedulerSignalerImpl              : Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-30 19:05:59.454  INFO 442152 --- [           main] o.q.c.QuartzScheduler                    : Quartz Scheduler v.2.3.2 created.
2025-07-30 19:05:59.454  INFO 442152 --- [           main] o.q.s.RAMJobStore                        : RAMJobStore initialized.
2025-07-30 19:05:59.454  INFO 442152 --- [           main] o.q.c.QuartzScheduler                    : Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-30 19:05:59.454  INFO 442152 --- [           main] o.q.i.StdSchedulerFactory                : Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-30 19:05:59.454  INFO 442152 --- [           main] o.q.i.StdSchedulerFactory                : Quartz scheduler version: 2.3.2
2025-07-30 19:05:59.455  INFO 442152 --- [           main] o.q.c.QuartzScheduler                    : JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@3d6c7b8a
RocketMQLog:WARN No appenders could be found for logger (io.netty.util.concurrent.GlobalEventExecutor).
RocketMQLog:WARN Please initialize the logger system properly.
2025-07-30 19:06:00.216  WARN 442152 --- [           main] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-30 19:06:00.223  INFO 442152 --- [           main] o.s.b.a.e.w.EndpointLinksResolver        : Exposing 21 endpoint(s) beneath base path '/actuator'
2025-07-30 19:06:00.315  INFO 442152 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 19:06:00.318  INFO 442152 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 19:06:00.366  WARN 442152 --- [           main] c.x.j.c.e.XxlJobExecutor                 : >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-07-30 19:06:00.432  INFO 442152 --- [           main] o.s.b.w.e.t.TomcatWebServer              : Tomcat started on port(s): 8102 (http) with context path ''
2025-07-30 19:06:00.442  INFO 442152 --- [      Thread-29] c.x.j.c.s.EmbedServer                    : >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 18102
2025-07-30 19:06:01.600  INFO 442152 --- [           main] o.s.c.c.u.InetUtils                      : Cannot determine local hostname
2025-07-30 19:06:01.757  INFO 442152 --- [           main] c.a.c.n.r.NacosServiceRegistry           : nacos registry, DEFAULT_GROUP skyeye-crm-dev *************:8102 register finished
2025-07-30 19:06:01.757  INFO 442152 --- [           main] o.s.s.q.SchedulerFactoryBean             : Starting Quartz Scheduler now
2025-07-30 19:06:01.758  INFO 442152 --- [           main] o.q.c.QuartzScheduler                    : Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-30 19:06:01.766  INFO 442152 --- [           main] c.CrmApplication                         : Started CrmApplication in 30.336 seconds (JVM running for 30.974)
2025-07-30 19:06:01.769  INFO 442152 --- [           main] c.s.c.b.l.SkyeyeStartListener            : ================================================================================
2025-07-30 19:06:01.769  INFO 442152 --- [           main] c.s.c.b.l.SkyeyeStartListener            : 开始加载 Skyeye 智能制造云办公管理系统 启动类
2025-07-30 19:06:01.769  INFO 442152 --- [           main] c.s.c.b.l.SkyeyeStartListener            : loadApiFromXmlListener start.
2025-07-30 19:06:01.769  INFO 442152 --- [           main] c.s.c.b.l.i.LoadApiFromXmlListener       : start loading xml [线程读取配置信息路径开始]
2025-07-30 19:06:01.770  INFO 442152 --- [           main] c.s.c.b.l.i.LoadApiFromXmlListener       : end loading xml [线程读取配置信息路径结束]
2025-07-30 19:06:01.770  INFO 442152 --- [           main] c.s.c.b.l.i.LoadApiFromXmlListener       : Read request path file completed: [0] interfaces in total
2025-07-30 19:06:01.770  INFO 442152 --- [           main] c.s.c.b.l.SkyeyeStartListener            : loadApiFromXmlListener executor end.
2025-07-30 19:06:01.770  INFO 442152 --- [           main] c.s.c.b.l.SkyeyeStartListener            : loadApiFromAnnoListener start.
2025-07-30 19:06:01.787  INFO 442152 --- [           main] c.s.c.b.l.i.LoadApiFromAnnoListener      : ApiListener start.
2025-07-30 19:06:01.930  INFO 442152 --- [           main] c.s.c.b.l.i.LoadApiFromAnnoListener      : anno load Rest API num is 65.
2025-07-30 19:06:01.930  INFO 442152 --- [           main] c.s.c.b.l.i.LoadApiFromAnnoListener      : anno load model num is 20.
2025-07-30 19:06:01.930  INFO 442152 --- [           main] c.s.c.b.l.i.LoadApiFromAnnoListener      : ApiListener end.
2025-07-30 19:06:01.930  INFO 442152 --- [           main] c.s.c.b.l.SkyeyeStartListener            : loadApiFromAnnoListener executor end.
2025-07-30 19:06:01.930  INFO 442152 --- [           main] c.s.c.b.l.SkyeyeStartListener            : loadClassEnumListener start.
2025-07-30 19:06:01.930  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read skyeye enum start.
2025-07-30 19:06:01.930  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read enum basePackage is com.skyeye.
2025-07-30 19:06:01.950  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.common.enumeration.CorrespondentEnterEnum.
2025-07-30 19:06:01.955  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.common.enumeration.CorrespondentEnterEnum enum, result is [{"enumFiledName":"SUPPLIER","isDefault":true,"name":"供应商","show":true,"id":"com.skyeye.supplier.service.impl.SupplierServiceImpl"},{"enumFiledName":"CUSTOM","isDefault":false,"name":"客户","show":true,"id":"com.skyeye.customer.service.impl.CustomerServiceImpl"}]
2025-07-30 19:06:01.956  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.common.enumeration.FlowableStateEnum.
2025-07-30 19:06:01.956  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.common.enumeration.FlowableStateEnum enum, result is [{"enumFiledName":"DRAFT","isDefault":true,"color":"black","name":"草稿","show":true,"id":"draft"},{"enumFiledName":"IN_EXAMINE","isDefault":false,"color":"blue","name":"审核中","show":true,"id":"inExamine"},{"enumFiledName":"PASS","isDefault":false,"color":"green","name":"审核通过","show":true,"id":"pass"},{"enumFiledName":"REJECT","isDefault":false,"color":"red","name":"驳回","show":true,"id":"reject"},{"enumFiledName":"INVALID","isDefault":false,"color":"red","name":"作废","show":true,"id":"invalid"},{"enumFiledName":"REVOKE","isDefault":false,"color":"orange","name":"撤销","show":true,"id":"revoke"}]
2025-07-30 19:06:01.956  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.common.enumeration.RequestType.
2025-07-30 19:06:01.956  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.common.enumeration.RequestType enum, result is [{"enumFiledName":"PC","isDefault":false,"name":"PC端请求","show":false,"id":"1"},{"enumFiledName":"APP","isDefault":false,"name":"手机端请求","show":false,"id":"2"}]
2025-07-30 19:06:01.956  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.payment.classenum.CrmPaymentCollectionAuthEnum.
2025-07-30 19:06:01.957  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.payment.classenum.CrmPaymentCollectionAuthEnum enum, result is [{"enumFiledName":"LIST","isDefault":false,"name":"查看列表","show":true,"id":"list"},{"enumFiledName":"ADD","isDefault":false,"name":"新增","show":true,"id":"add"},{"enumFiledName":"EDIT","isDefault":false,"name":"编辑","show":true,"id":"edit"},{"enumFiledName":"DELETE","isDefault":false,"name":"删除","show":true,"id":"delete"},{"enumFiledName":"REVOKE","isDefault":false,"name":"撤销","show":true,"id":"revoke"},{"enumFiledName":"INVALID","isDefault":false,"name":"作废","show":true,"id":"invalid"},{"enumFiledName":"SUBMIT_TO_APPROVAL","isDefault":false,"name":"提交审批","show":true,"id":"submitToApproval"}]
2025-07-30 19:06:01.957  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.common.enumeration.WeekDay.
2025-07-30 19:06:01.957  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.common.enumeration.WeekDay enum, result is [{"enumFiledName":"MON","isDefault":false,"name":"星期一","show":true,"id":1},{"enumFiledName":"TUE","isDefault":false,"name":"星期二","show":true,"id":2},{"enumFiledName":"WED","isDefault":false,"name":"星期三","show":true,"id":3},{"enumFiledName":"THU","isDefault":false,"name":"星期四","show":true,"id":4},{"enumFiledName":"FRI","isDefault":false,"name":"星期五","show":true,"id":5},{"enumFiledName":"SAT","isDefault":false,"name":"星期六","show":true,"id":6},{"enumFiledName":"SUN","isDefault":false,"name":"星期日","show":true,"id":7}]
2025-07-30 19:06:01.957  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.common.enumeration.CorrespondentAllEnterEnum.
2025-07-30 19:06:01.957  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.common.enumeration.CorrespondentAllEnterEnum enum, result is [{"enumFiledName":"SUPPLIER","isDefault":true,"name":"供应商","show":true,"id":"com.skyeye.supplier.service.impl.SupplierServiceImpl"},{"enumFiledName":"CUSTOM","isDefault":false,"name":"客户","show":true,"id":"com.skyeye.customer.service.impl.CustomerServiceImpl"},{"enumFiledName":"MEMBER","isDefault":false,"name":"会员","show":true,"id":"com.skyeye.service.impl.MemberServiceImpl"}]
2025-07-30 19:06:01.957  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.invoice.classenum.CrmInvoiceHeaderAuthEnum.
2025-07-30 19:06:01.957  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.invoice.classenum.CrmInvoiceHeaderAuthEnum enum, result is [{"enumFiledName":"ADD","isDefault":false,"name":"新增","show":true,"id":"add"},{"enumFiledName":"EDIT","isDefault":false,"name":"编辑","show":true,"id":"edit"},{"enumFiledName":"DELETE","isDefault":false,"name":"删除","show":true,"id":"delete"}]
2025-07-30 19:06:01.957  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.common.enumeration.HttpMethodEnum.
2025-07-30 19:06:01.958  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.common.enumeration.HttpMethodEnum enum, result is [{"enumFiledName":"GET_REQUEST","isDefault":true,"name":"GET请求","show":true,"id":"GET"},{"enumFiledName":"POST_REQUEST","isDefault":false,"name":"POST请求","show":true,"id":"POST"},{"enumFiledName":"DELETE_REQUEST","isDefault":false,"name":"DELETE请求","show":true,"id":"DELETE"},{"enumFiledName":"PUT_REQUEST","isDefault":false,"name":"PUT请求","show":true,"id":"PUT"}]
2025-07-30 19:06:01.958  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.eve.flowable.classenum.FormSubType.
2025-07-30 19:06:01.958  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.eve.flowable.classenum.FormSubType enum, result is [{"enumFiledName":"DRAFT","isDefault":false,"name":"保存草稿","show":false,"id":1},{"enumFiledName":"SUB_FLOWABLE","isDefault":false,"name":"提交到工作流","show":false,"id":2}]
2025-07-30 19:06:01.958  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.common.enumeration.SessionStatus.
2025-07-30 19:06:01.958  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.common.enumeration.SessionStatus enum, result is [{"enumFiledName":"APINOTFOUND","isDefault":true,"name":"API不存在","show":true},{"enumFiledName":"TIMEOUT","isDefault":false,"name":"会话超时","show":true},{"enumFiledName":"NOAUTHPOINT","isDefault":false,"name":"无权限","show":true}]
2025-07-30 19:06:01.958  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.common.enumeration.NoticeUserMessageTypeEnum.
2025-07-30 19:06:01.959  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.common.enumeration.NoticeUserMessageTypeEnum enum, result is [{"enumFiledName":"SCHEDULE_MESSAGE","isDefault":true,"name":"日程消息","show":true,"id":1},{"enumFiledName":"PLAN_REMINDER","isDefault":false,"name":"计划提醒","show":true,"id":2},{"enumFiledName":"WORK_ORDER_REMINDER","isDefault":false,"name":"工单派工提醒","show":true,"id":3}]
2025-07-30 19:06:01.959  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.opportunity.classenum.CrmOpportunityAuthEnum.
2025-07-30 19:06:01.959  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.opportunity.classenum.CrmOpportunityAuthEnum enum, result is [{"enumFiledName":"LIST","isDefault":false,"name":"查看列表","show":true,"id":"list"},{"enumFiledName":"ADD","isDefault":false,"name":"新增","show":true,"id":"add"},{"enumFiledName":"EDIT","isDefault":false,"name":"编辑","show":true,"id":"edit"},{"enumFiledName":"DELETE","isDefault":false,"name":"删除","show":true,"id":"delete"},{"enumFiledName":"REVOKE","isDefault":false,"name":"撤销","show":true,"id":"revoke"},{"enumFiledName":"INVALID","isDefault":false,"name":"作废","show":true,"id":"invalid"},{"enumFiledName":"SUBMIT_TO_APPROVAL","isDefault":false,"name":"提交审批","show":true,"id":"submitToApproval"},{"enumFiledName":"CONMUNICATE","isDefault":false,"name":"初期沟通","show":true,"id":"conmunicate"},{"enumFiledName":"QUOTED_PRICE","isDefault":false,"name":"方案与报价","show":true,"id":"quotedPrice"},{"enumFiledName":"TENDER","isDefault":false,"name":"竞争与投标","show":true,"id":"tender"},{"enumFiledName":"NEGOTIATE","isDefault":false,"name":"商务谈判","show":true,"id":"negotiate"},{"enumFiledName":"TURNOVER","isDefault":false,"name":"成交","show":true,"id":"turnover"},{"enumFiledName":"LOSING_TABLE","isDefault":false,"name":"丢单","show":true,"id":"losingTable"},{"enumFiledName":"LAY_ASIDE","isDefault":false,"name":"搁置","show":true,"id":"layAside"}]
2025-07-30 19:06:01.959  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.common.enumeration.RegistrationType.
2025-07-30 19:06:01.959  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.common.enumeration.RegistrationType enum, result is [{"enumFiledName":"AGRICULTURAL","isDefault":true,"name":"农业户口","show":true,"id":1},{"enumFiledName":"NON_AGRICULTURAL","isDefault":false,"name":"非农业户口","show":true,"id":2}]
2025-07-30 19:06:01.959  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.opportunity.classenum.CrmOpportunityStateEnum.
2025-07-30 19:06:01.960  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.opportunity.classenum.CrmOpportunityStateEnum enum, result is [{"enumFiledName":"INITIAL_COMMUNICATION","isDefault":false,"name":"初期沟通","show":true,"id":"initialCommunication"},{"enumFiledName":"SCHEME_AND_QUOTATION","isDefault":false,"name":"方案与报价","show":true,"id":"schemeAndQuotation"},{"enumFiledName":"COMPETITION_AND_BIDDING","isDefault":false,"name":"竞争与投标","show":true,"id":"competitionAndBidding"},{"enumFiledName":"BUSINESS_NEGOTIATION","isDefault":false,"name":"商务谈判","show":true,"id":"businessNegotiation"},{"enumFiledName":"STRIKE_BARGAIN","isDefault":false,"name":"成交","show":true,"id":"strikeBargain"},{"enumFiledName":"LOST_ORDER","isDefault":false,"name":"丢单","show":true,"id":"lostOrder"},{"enumFiledName":"LAY_ASIDE","isDefault":false,"name":"搁置","show":true,"id":"layAside"},{"enumFiledName":"DRAFT","isDefault":true,"color":"black","name":"草稿","show":true,"id":"draft"},{"enumFiledName":"IN_EXAMINE","isDefault":false,"color":"blue","name":"审核中","show":true,"id":"inExamine"},{"enumFiledName":"PASS","isDefault":false,"color":"green","name":"审核通过","show":true,"id":"pass"},{"enumFiledName":"REJECT","isDefault":false,"color":"red","name":"驳回","show":true,"id":"reject"},{"enumFiledName":"INVALID","isDefault":false,"color":"red","name":"作废","show":true,"id":"invalid"},{"enumFiledName":"REVOKE","isDefault":false,"color":"orange","name":"撤销","show":true,"id":"revoke"}]
2025-07-30 19:06:01.960  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.common.enumeration.ScheduleDayObjectType.
2025-07-30 19:06:01.960  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.common.enumeration.ScheduleDayObjectType enum, result is [{"enumFiledName":"PLAN","isDefault":false,"name":"任务计划","show":true,"id":1},{"enumFiledName":"PRO_TASK","isDefault":false,"name":"项目任务","show":true,"id":2}]
2025-07-30 19:06:01.960  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.common.enumeration.SexEnum.
2025-07-30 19:06:01.960  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.common.enumeration.SexEnum enum, result is [{"enumFiledName":"KEEP_BACK","isDefault":true,"name":"保密","show":true,"id":0},{"enumFiledName":"MAN","isDefault":false,"name":"男","show":true,"id":1},{"enumFiledName":"WOMAN","isDefault":false,"name":"女","show":true,"id":2}]
2025-07-30 19:06:01.960  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.contract.classenum.CrmContractStateEnum.
2025-07-30 19:06:01.960  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.contract.classenum.CrmContractStateEnum enum, result is [{"enumFiledName":"EXECUTING","isDefault":false,"name":"执行中","show":true,"id":"executing"},{"enumFiledName":"CLOSE","isDefault":false,"name":"关闭","show":true,"id":"close"},{"enumFiledName":"LAY_ASIDE","isDefault":false,"name":"搁置","show":true,"id":"layAside"},{"enumFiledName":"DRAFT","isDefault":true,"color":"black","name":"草稿","show":true,"id":"draft"},{"enumFiledName":"IN_EXAMINE","isDefault":false,"color":"blue","name":"审核中","show":true,"id":"inExamine"},{"enumFiledName":"PASS","isDefault":false,"color":"green","name":"审核通过","show":true,"id":"pass"},{"enumFiledName":"REJECT","isDefault":false,"color":"red","name":"驳回","show":true,"id":"reject"},{"enumFiledName":"INVALID","isDefault":false,"color":"red","name":"作废","show":true,"id":"invalid"},{"enumFiledName":"REVOKE","isDefault":false,"color":"orange","name":"撤销","show":true,"id":"revoke"}]
2025-07-30 19:06:01.960  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.common.enumeration.FlowableChildStateEnum.
2025-07-30 19:06:01.961  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.common.enumeration.FlowableChildStateEnum enum, result is [{"enumFiledName":"DRAFT","isDefault":true,"name":"未提交审批","show":true,"id":"0"},{"enumFiledName":"IN_EXAMINE","isDefault":false,"name":"审批中","show":true,"id":"1"},{"enumFiledName":"INSUFFICIENT","isDefault":false,"name":"不满足条件，审核失败","show":true,"id":"2"},{"enumFiledName":"ADEQUATE","isDefault":false,"name":"满足条件，审核成功","show":true,"id":"3"},{"enumFiledName":"REJECT","isDefault":false,"name":"驳回","show":true,"id":"4"}]
2025-07-30 19:06:01.961  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.contract.classenum.CrmContractAuthEnum.
2025-07-30 19:06:01.961  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.contract.classenum.CrmContractAuthEnum enum, result is [{"enumFiledName":"LIST","isDefault":false,"name":"查看列表","show":true,"id":"list"},{"enumFiledName":"ADD","isDefault":false,"name":"新增","show":true,"id":"add"},{"enumFiledName":"EDIT","isDefault":false,"name":"编辑","show":true,"id":"edit"},{"enumFiledName":"DELETE","isDefault":false,"name":"删除","show":true,"id":"delete"},{"enumFiledName":"REVOKE","isDefault":false,"name":"撤销","show":true,"id":"revoke"},{"enumFiledName":"INVALID","isDefault":false,"name":"作废","show":true,"id":"invalid"},{"enumFiledName":"SUBMIT_TO_APPROVAL","isDefault":false,"name":"提交审批","show":true,"id":"submitToApproval"},{"enumFiledName":"PERFORM","isDefault":false,"name":"执行","show":true,"id":"perform"},{"enumFiledName":"CLOSE","isDefault":false,"name":"关闭","show":true,"id":"close"},{"enumFiledName":"LAY_ASIDE","isDefault":false,"name":"搁置","show":true,"id":"layAside"},{"enumFiledName":"RECOVERY","isDefault":false,"name":"恢复","show":true,"id":"recovery"}]
2025-07-30 19:06:01.961  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.common.enumeration.ApplicableObjectsType.
2025-07-30 19:06:01.961  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.common.enumeration.ApplicableObjectsType enum, result is [{"enumFiledName":"STAFF","isDefault":true,"name":"员工","show":true,"id":1},{"enumFiledName":"DEPARTMENT","isDefault":false,"name":"部门","show":true,"id":2},{"enumFiledName":"COMPANY","isDefault":false,"name":"企业","show":true,"id":3}]
2025-07-30 19:06:01.961  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.common.enumeration.IsUsedEnum.
2025-07-30 19:06:01.961  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.common.enumeration.IsUsedEnum enum, result is [{"enumFiledName":"NOT_USED","isDefault":true,"color":"#FFC107","name":"未使用","show":true,"id":0},{"enumFiledName":"IN_USE","isDefault":false,"color":"#4CAF50","name":"已使用","show":true,"id":1}]
2025-07-30 19:06:01.961  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.common.enumeration.HealthStatus.
2025-07-30 19:06:01.962  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.common.enumeration.HealthStatus enum, result is [{"enumFiledName":"HEALTH","isDefault":true,"name":"健康","show":true,"id":1},{"enumFiledName":"GOOD","isDefault":false,"name":"良好","show":true,"id":2},{"enumFiledName":"GENERAL","isDefault":false,"name":"一般","show":true,"id":3},{"enumFiledName":"WEAK","isDefault":false,"name":"较弱","show":true,"id":4},{"enumFiledName":"OTHER","isDefault":false,"name":"其他","show":true,"id":5}]
2025-07-30 19:06:01.962  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.common.enumeration.IsDefaultEnum.
2025-07-30 19:06:01.962  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.common.enumeration.IsDefaultEnum enum, result is [{"enumFiledName":"IS_DEFAULT","isDefault":true,"color":"green","name":"是","show":true,"id":1},{"enumFiledName":"NOT_DEFAULT","isDefault":false,"color":"red","name":"否","show":true,"id":2}]
2025-07-30 19:06:01.962  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.common.enumeration.IDCardType.
2025-07-30 19:06:01.962  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.common.enumeration.IDCardType enum, result is [{"enumFiledName":"ID_CARD","isDefault":true,"name":"居民身份证","show":true,"id":1},{"enumFiledName":"OTHER","isDefault":false,"name":"其他","show":true,"id":10}]
2025-07-30 19:06:01.962  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.common.enumeration.ApiAllUseState.
2025-07-30 19:06:01.962  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.common.enumeration.ApiAllUseState enum, result is [{"enumFiledName":"LOGIN_AND_AUTH","isDefault":true,"name":"需要登录并且赋权","show":true,"id":"1"},{"enumFiledName":"LOGIN_NOT_AUTH","isDefault":false,"name":"需要登录不需要赋权","show":true,"id":"2"},{"enumFiledName":"NOT","isDefault":false,"name":"既不需要登录也不需要赋权","show":true,"id":"0"}]
2025-07-30 19:06:01.962  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.documentary.classenum.CrmDocumentaryAuthEnum.
2025-07-30 19:06:01.962  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.documentary.classenum.CrmDocumentaryAuthEnum enum, result is [{"enumFiledName":"ADD","isDefault":false,"name":"新增","show":true,"id":"add"},{"enumFiledName":"EDIT","isDefault":false,"name":"编辑","show":true,"id":"edit"},{"enumFiledName":"DELETE","isDefault":false,"name":"删除","show":true,"id":"delete"}]
2025-07-30 19:06:01.962  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.common.enumeration.PayTypeEnum.
2025-07-30 19:06:01.962  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.common.enumeration.PayTypeEnum enum, result is [{"enumFiledName":"CASH","isDefault":true,"name":"现金","show":true,"id":1},{"enumFiledName":"ACCOUNTING","isDefault":false,"name":"记账","show":true,"id":2},{"enumFiledName":"OTHER","isDefault":false,"name":"其他","show":true,"id":3}]
2025-07-30 19:06:01.962  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.common.enumeration.ShopMaterialDeliveryMethod.
2025-07-30 19:06:01.963  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.common.enumeration.ShopMaterialDeliveryMethod enum, result is [{"enumFiledName":"DEFAULT_SET","isDefault":false,"name":"快递发货","show":true,"id":1},{"enumFiledName":"SINGLE_SET","isDefault":false,"name":"用户自提","show":true,"id":2},{"enumFiledName":"LOCAL_SET","isDefault":false,"name":"同城配送","show":true,"id":3}]
2025-07-30 19:06:01.963  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.common.enumeration.VerificationParamsEnum.
2025-07-30 19:06:01.963  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.common.enumeration.VerificationParamsEnum enum, result is [{"enumFiledName":"REQUIRED","isDefault":false,"formerRequirement":"required","method":"isBlank","name":"非空限制","show":true,"id":"required","regular":"","desc":"参数非空校验","resultMsg":"不能为空"},{"enumFiledName":"NUM","isDefault":false,"formerRequirement":"number","method":"regularCheck","name":"数字限制","show":true,"id":"num","regular":"^(\\-|\\+)?\\d+(\\.\\d+)?$","desc":"数字校验","resultMsg":"数字类型不正确"},{"enumFiledName":"DATE","isDefault":false,"formerRequirement":"date","method":"isDate","name":"日期限制","show":true,"id":"date","regular":"","desc":"时间校验,格式：yyyy-MM-dd HH:mm:ss","resultMsg":"时间类型不正确"},{"enumFiledName":"EMAIL","isDefault":false,"formerRequirement":"email","method":"regularCheck","name":"邮箱限制","show":true,"id":"email","regular":"^([\\w-\\.]+)@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.)|(([\\w-]+\\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\\]?)$","desc":"邮箱校验","resultMsg":"邮箱类型不正确"},{"enumFiledName":"IDCARD","isDefault":false,"formerRequirement":"identity","method":"regularCheck","name":"身份证限制","show":true,"id":"idcard","regular":"(^\\d{18}$)|(^\\d{15}$)|(^\\d{17}(x|X|\\d)$)","desc":"证件号校验","resultMsg":"证件号类型不正确"},{"enumFiledName":"PHONE","isDefault":false,"formerRequirement":"phone","method":"regularCheck","name":"手机号限制","show":true,"id":"phone","regular":"^((13[0-9])|(15[^4])|(16[0-9])|(19[0-9])|(18[0-9])|(17[0-8])|(147))\\d{8}$","desc":"手机号校验","resultMsg":"手机号类型不正确"},{"enumFiledName":"URL","isDefault":false,"formerRequirement":"url","method":"regularCheck","name":"链接格式限制","show":true,"id":"url","regular":"http(s)?://([\\w-]+\\.)+[\\w-]+(/[\\w- ./?%&=]*)?","desc":"url校验","resultMsg":"url类型不正确"},{"enumFiledName":"IP","isDefault":false,"formerRequirement":"","method":"regularCheck","name":"ip校验","show":false,"id":"ip","regular":"^(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)$","desc":"ip校验","resultMsg":"ip类型不正确"},{"enumFiledName":"POSTCODE","isDefault":false,"formerRequirement":"postcode","method":"regularCheck","name":"邮编限制","show":true,"id":"postcode","regular":"^\\d{6}$","desc":"国内邮编校验","resultMsg":"国内邮编类型不正确"},{"enumFiledName":"DOUBLE","isDefault":false,"formerRequirement":"double","method":"regularCheck","name":"金钱校验","show":true,"id":"double","regular":"^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){0,6})?$","desc":"金钱校验","resultMsg":"小数格式类型不正确"},{"enumFiledName":"PERCENTAGE","isDefault":false,"formerRequirement":"percentage","method":"regularCheck","name":"百分比小数校验","show":true,"id":"percentage","regular":"^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){0,2})?$","desc":"百分比小数校验","resultMsg":"百分比不正确"},{"enumFiledName":"VINCODE","isDefault":false,"formerRequirement":"vincode","method":"regularCheck","name":"VIN码校验","show":true,"id":"vincode","regular":"^(?![^A-Z]+$)(?![^0-9]+$)[1-9WTJSKLVRYZ]{1}[A-HJ-NPR-Z0-9]{16}$","desc":"VIN码校验","resultMsg":"VIN码不正确"},{"enumFiledName":"PLATENO","isDefault":false,"formerRequirement":"plateno","method":"regularCheck","name":"车牌号校验","show":true,"id":"plateno","regular":"^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$","desc":"车牌号校验","resultMsg":"车牌号不正确"},{"enumFiledName":"JSON","isDefault":false,"formerRequirement":"","method":"isJson","name":"json字符串校验","show":false,"id":"json","regular":"","desc":"json字符串校验","resultMsg":"参数类型非json"}]
2025-07-30 19:06:01.963  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.common.enumeration.AbnormalCheckworkType.
2025-07-30 19:06:01.963  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.common.enumeration.AbnormalCheckworkType enum, result is [{"enumFiledName":"ABNORMAL_LEAVEEARLY","isDefault":false,"name":"早退","show":true,"id":"leaveEarly"},{"enumFiledName":"ABNORMAL_LATE","isDefault":false,"name":"迟到","show":true,"id":"late"},{"enumFiledName":"ABNORMAL_MINER","isDefault":false,"name":"旷工","show":true,"id":"miner"}]
2025-07-30 19:06:01.963  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.common.enumeration.SearchParamsConfigDataType.
2025-07-30 19:06:01.963  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.common.enumeration.SearchParamsConfigDataType enum, result is [{"enumFiledName":"INPUT","isDefault":true,"name":"文本","show":true,"id":"input"},{"enumFiledName":"DATE","isDefault":false,"name":"日期","show":true,"id":"date"},{"enumFiledName":"USER","isDefault":false,"name":"用户","show":true,"id":"user"},{"enumFiledName":"USER_STAFF","isDefault":false,"name":"员工","show":true,"id":"userStaff"},{"enumFiledName":"VIRTUAL_SELECT","isDefault":false,"name":"接口-下拉框","show":true,"id":"virtualSelect"},{"enumFiledName":"CONSTANT_SELECT","isDefault":false,"name":"常量-下拉框","show":true,"id":"constantSelect"}]
2025-07-30 19:06:01.964  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.common.enumeration.BloodTypeEnum.
2025-07-30 19:06:01.964  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.common.enumeration.BloodTypeEnum enum, result is [{"enumFiledName":"A","isDefault":true,"name":"A型","show":true,"id":1},{"enumFiledName":"B","isDefault":false,"name":"B型","show":true,"id":2},{"enumFiledName":"AB","isDefault":false,"name":"AB型","show":true,"id":3},{"enumFiledName":"O","isDefault":false,"name":"O型","show":true,"id":4},{"enumFiledName":"OTHER","isDefault":false,"name":"其他","show":true,"id":5},{"enumFiledName":"UNDETERMINED","isDefault":false,"name":"未定","show":true,"id":6}]
2025-07-30 19:06:01.964  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.common.enumeration.UserQuitType.
2025-07-30 19:06:01.964  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.common.enumeration.UserQuitType enum, result is [{"enumFiledName":"AUTOMATIC_RESIGNATION","isDefault":false,"name":"自动离职","show":true,"id":1},{"enumFiledName":"retire","isDefault":false,"name":"退休","show":true,"id":2},{"enumFiledName":"DISEASE_WORDS","isDefault":false,"name":"病辞","show":true,"id":3},{"enumFiledName":"dismiss","isDefault":false,"name":"辞退","show":true,"id":4},{"enumFiledName":"resignation","isDefault":false,"name":"辞职","show":true,"id":5}]
2025-07-30 19:06:01.964  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.common.enumeration.QRCodeLinkType.
2025-07-30 19:06:01.964  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.common.enumeration.QRCodeLinkType enum, result is [{"enumFiledName":"SUBJECT_CLASSES","isDefault":true,"name":"学校模块-课程与班级关系","show":true,"id":"subjectClasses"},{"enumFiledName":"STUDENT_CHECKWORK","isDefault":true,"name":"学校模块-考勤签到","show":true,"id":"studentCheckwork"}]
2025-07-30 19:06:01.964  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.common.enumeration.WinterVacationType.
2025-07-30 19:06:01.964  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.common.enumeration.WinterVacationType enum, result is [{"enumFiledName":"ONE_YEAR","isDefault":false,"min":0,"max":1,"name":"1年以下","show":true,"id":1},{"enumFiledName":"ONE_THREE_YEAR","isDefault":false,"min":1,"max":3,"name":"1年 ~ 3年","show":true,"id":2},{"enumFiledName":"THREE_FIVE_YEAR","isDefault":false,"min":3,"max":5,"name":"3年 ~ 5年","show":true,"id":3},{"enumFiledName":"FIVE_EIGHT_YEAR","isDefault":false,"min":5,"max":8,"name":"5年 ~ 8年","show":true,"id":4},{"enumFiledName":"EIGHT_YEAR","isDefault":false,"min":8,"max":100,"name":"8年以上","show":true,"id":5}]
2025-07-30 19:06:01.964  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.contract.classenum.CrmContractChildStateEnum.
2025-07-30 19:06:01.964  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.contract.classenum.CrmContractChildStateEnum enum, result is [{"enumFiledName":"PENDING_ORDER","isDefault":false,"name":"待下达订单","show":true,"id":"pendingOrder"},{"enumFiledName":"PARTIAL_RELEASE","isDefault":false,"name":"部分下达","show":true,"id":"partialRelease"},{"enumFiledName":"ALL_ISSUED","isDefault":false,"name":"全部下达","show":true,"id":"allIssued"}]
2025-07-30 19:06:01.964  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.invoice.classenum.CrmInvoiceAuthEnum.
2025-07-30 19:06:01.964  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.invoice.classenum.CrmInvoiceAuthEnum enum, result is [{"enumFiledName":"LIST","isDefault":false,"name":"查看列表","show":true,"id":"list"},{"enumFiledName":"ADD","isDefault":false,"name":"新增","show":true,"id":"add"},{"enumFiledName":"EDIT","isDefault":false,"name":"编辑","show":true,"id":"edit"},{"enumFiledName":"DELETE","isDefault":false,"name":"删除","show":true,"id":"delete"},{"enumFiledName":"REVOKE","isDefault":false,"name":"撤销","show":true,"id":"revoke"},{"enumFiledName":"INVALID","isDefault":false,"name":"作废","show":true,"id":"invalid"},{"enumFiledName":"SUBMIT_TO_APPROVAL","isDefault":false,"name":"提交审批","show":true,"id":"submitToApproval"}]
2025-07-30 19:06:01.964  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.common.enumeration.SmsSceneEnum.
2025-07-30 19:06:01.965  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.common.enumeration.SmsSceneEnum enum, result is [{"enumFiledName":"LOGIN","isDefault":false,"name":"user-sms-login","show":true,"remark":"用户 - 手机号登陆","id":1},{"enumFiledName":"UPDATE_MOBILE","isDefault":false,"name":"user-update-mobile","show":true,"remark":"用户 - 修改绑定的手机","id":2},{"enumFiledName":"UPDATE_PASSWORD","isDefault":false,"name":"user-update-password","show":true,"remark":"用户 - 修改密码","id":3},{"enumFiledName":"RESET_PASSWORD","isDefault":false,"name":"user-reset-password","show":true,"remark":"用户 - 忘记密码","id":4}]
2025-07-30 19:06:01.965  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.contract.classenum.CrmContractFromType.
2025-07-30 19:06:01.965  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.contract.classenum.CrmContractFromType enum, result is [{"enumFiledName":"PURCHASE_REQUEST","isDefault":false,"name":"TODO 待定","show":true,"id":1}]
2025-07-30 19:06:01.965  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.common.enumeration.WhetherEnum.
2025-07-30 19:06:01.965  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.common.enumeration.WhetherEnum enum, result is [{"enumFiledName":"ENABLE_USING","isDefault":true,"name":"是","show":true,"id":1},{"enumFiledName":"DISABLE_USING","isDefault":false,"name":"否","show":true,"id":0}]
2025-07-30 19:06:01.965  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.common.enumeration.DeleteFlagEnum.
2025-07-30 19:06:01.965  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.common.enumeration.DeleteFlagEnum enum, result is [{"enumFiledName":"NOT_DELETE","isDefault":false,"name":"未删除","show":false,"id":0},{"enumFiledName":"DELETED","isDefault":false,"name":"删除","show":false,"id":1}]
2025-07-30 19:06:01.965  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.attr.classenum.AttrSymbols.
2025-07-30 19:06:01.965  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.attr.classenum.AttrSymbols enum, result is [{"enumFiledName":"LESS_THAN","isDefault":true,"name":"小于","show":true,"id":"lessThan","symbols":"<"},{"enumFiledName":"GREATER_THAN","isDefault":false,"name":"大于","show":true,"id":"greaterThan","symbols":">"},{"enumFiledName":"EQUAL_TO","isDefault":false,"name":"等于","show":true,"id":"equalTo","symbols":"=="},{"enumFiledName":"NOT_EQUAL","isDefault":false,"name":"不等于","show":true,"id":"notEqual","symbols":"!="},{"enumFiledName":"LESS_THAN_OR_EQUAL","isDefault":false,"name":"小于等于","show":true,"id":"lessThanOrEqual","symbols":"<="},{"enumFiledName":"GREATER_THAN_OR_EQUAL","isDefault":false,"name":"大于等于","show":true,"id":"greaterThanOrEqual","symbols":">="},{"enumFiledName":"CONTAIN","isDefault":false,"name":"包含","show":true,"id":"contain","symbols":"contain"}]
2025-07-30 19:06:01.965  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.common.enumeration.TenantEnum.
2025-07-30 19:06:01.965  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.common.enumeration.TenantEnum enum, result is [{"enumFiledName":"STRONG_ISOLATION","isDefault":false,"name":"强制隔离，只查询当前租户的数据","show":true,"id":"strongIsolation"},{"enumFiledName":"WEAK_ISOLATION","isDefault":false,"name":"弱隔离，查询当前租户和平台的数据","show":true,"id":"weakIsolation"},{"enumFiledName":"NO_ISOLATION","isDefault":false,"name":"不做隔离","show":true,"id":"noIsolation"},{"enumFiledName":"PLATE","isDefault":false,"name":"只有平台的账号可以查看","show":true,"id":"plate"}]
2025-07-30 19:06:01.965  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.common.enumeration.CheckDayType.
2025-07-30 19:06:01.966  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.common.enumeration.CheckDayType enum, result is [{"enumFiledName":"DAY_IS_PERSONAL","isDefault":false,"color":"#63B8FF","name":"个人","show":true,"className":"","id":1},{"enumFiledName":"DAY_IS_WORK","isDefault":false,"color":"#CD69C9","name":"工作","show":true,"className":"","id":2},{"enumFiledName":"DAY_IS_HOLIDAY","isDefault":false,"color":"#54FF9F","name":"节假日","show":true,"className":"xiu","id":3},{"enumFiledName":"DAY_IS_BIRTHDAY","isDefault":false,"color":"#FF0000","name":"生日","show":true,"className":"","id":4},{"enumFiledName":"DAY_IS_CUSTOM","isDefault":false,"color":"#ADADAD","name":"自定义","show":true,"className":"","id":5},{"enumFiledName":"DAY_IS_WORKING","isDefault":false,"color":"","name":"工作日","show":true,"className":"work-time","id":6},{"enumFiledName":"DAY_IS_WORK_OVERTIME","isDefault":false,"color":"","name":"加班","show":true,"className":"work-overtime","id":7},{"enumFiledName":"DAY_IS_LEAVE","isDefault":false,"color":"","name":"请假","show":true,"className":"leave","id":8},{"enumFiledName":"DAY_IS_BUSINESS_TRAVEL","isDefault":false,"color":"","name":"出差","show":true,"className":"business-travel","id":9}]
2025-07-30 19:06:01.966  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.common.enumeration.SearchOperator.
2025-07-30 19:06:01.966  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.common.enumeration.SearchOperator enum, result is [{"enumFiledName":"LESS_THAN","isDefault":true,"name":"大于","show":true,"id":">","sql":" AND DATE_FORMAT(表.字段, '%Y-%m-%d') > '?'"},{"enumFiledName":"GREATER_THAN","isDefault":false,"name":"小于","show":true,"id":"<","sql":" AND DATE_FORMAT(表.字段,'%Y-%m-%d') < '?'"},{"enumFiledName":"EQUAL","isDefault":true,"name":"等于","show":true,"id":"=","sql":" AND DATE_FORMAT(表.字段,'%Y-%m-%d') = '?'"},{"enumFiledName":"NOT_EQUAL","isDefault":false,"name":"不等于","show":true,"id":"!=","sql":" AND DATE_FORMAT(表.字段,'%Y-%m-%d') != '?'"},{"enumFiledName":"LESS_THAN_OR_EQUAL","isDefault":false,"name":"小于等于","show":true,"id":"<=","sql":" AND DATE_FORMAT(表.字段,'%Y-%m-%d') <= '?'"},{"enumFiledName":"GREATER_THAN_OR_EQUAL","isDefault":false,"name":"大于等于","show":true,"id":">=","sql":" AND DATE_FORMAT(表.字段,'%Y-%m-%d') >= '?'"},{"enumFiledName":"IN","isDefault":false,"name":"包含","show":true,"id":"in","sql":""},{"enumFiledName":"LIKE","isDefault":false,"name":"相似","show":true,"id":"like","sql":" AND 表.字段 LIKE '%?%'"},{"enumFiledName":"NOT_LIKE","isDefault":false,"name":"不相似","show":true,"id":"not like","sql":" 表.字段 NOT LIKE '%?%'"},{"enumFiledName":"START_WITH","isDefault":false,"name":"以...开头","show":true,"id":"start with","sql":" AND 表.字段 LIKE '?%'"},{"enumFiledName":"END_WITH","isDefault":false,"name":"以...结束","show":true,"id":"end with","sql":" AND 表.字段 LIKE '%?'"},{"enumFiledName":"BETWEEN","isDefault":false,"name":"区间","show":true,"id":"between","sql":" AND DATE_FORMAT(表.字段, '%Y-%m-%d') >= '?' AND DATE_FORMAT(表.字段, '%Y-%m-%d') <= '?'"}]
2025-07-30 19:06:01.966  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.common.enumeration.UserStaffState.
2025-07-30 19:06:01.966  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.common.enumeration.UserStaffState enum, result is [{"enumFiledName":"ON_THE_JOB","isDefault":false,"color":"green","name":"在职(转正的员工)","show":true,"id":1},{"enumFiledName":"QUIT","isDefault":false,"color":"red","name":"离职","show":true,"id":2},{"enumFiledName":"PROBATION","isDefault":false,"color":"blue","name":"见习(用于实习生)","show":true,"id":3},{"enumFiledName":"PROBATION_PERIOD","isDefault":false,"color":"blue","name":"试用期(用于未转正的员工)","show":true,"id":4},{"enumFiledName":"RETIRE","isDefault":false,"color":"gray","name":"退休","show":true,"id":5}]
2025-07-30 19:06:01.966  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.common.enumeration.OvertimeSettlementType.
2025-07-30 19:06:01.966  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.common.enumeration.OvertimeSettlementType enum, result is [{"enumFiledName":"SINGLE_SALARY_SETTLEMENT","isDefault":true,"name":"单倍薪资结算","show":true,"id":1},{"enumFiledName":"ONE_POINT_FIVE_SALARY_SETTLEMENT","isDefault":false,"name":"1.5倍薪资结算","show":true,"id":2},{"enumFiledName":"DOUBLE_SALARY_SETTLEMENT","isDefault":false,"name":"双倍薪资结算","show":true,"id":3},{"enumFiledName":"COMPENSATORY_LEAVE_SETTLEMENT","isDefault":false,"name":"补休结算","show":true,"id":6}]
2025-07-30 19:06:01.966  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.common.enumeration.EnableEnum.
2025-07-30 19:06:01.966  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.common.enumeration.EnableEnum enum, result is [{"enumFiledName":"ENABLE_USING","isDefault":true,"color":"green","name":"启用","show":true,"id":1},{"enumFiledName":"DISABLE_USING","isDefault":false,"color":"red","name":"禁用","show":true,"id":2}]
2025-07-30 19:06:01.966  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.follow.classenum.CrmFollowUpAuthEnum.
2025-07-30 19:06:01.966  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.follow.classenum.CrmFollowUpAuthEnum enum, result is [{"enumFiledName":"ADD","isDefault":false,"name":"新增","show":true,"id":"add"},{"enumFiledName":"EDIT","isDefault":false,"name":"编辑","show":true,"id":"edit"},{"enumFiledName":"DELETE","isDefault":false,"name":"删除","show":true,"id":"delete"}]
2025-07-30 19:06:01.966  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.common.enumeration.WeekTypeEnum.
2025-07-30 19:06:01.967  INFO 442152 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read com.skyeye.common.enumeration.WeekTypeEnum enum, result is [{"enumFiledName":"ODD_WEEKS","isDefault":false,"name":"单周","show":false,"id":0},{"enumFiledName":"BIWEEKLY","isDefault":false,"name":"双周","show":false,"id":1}]
2025-07-30 19:06:01.977 DEBUG 442152 --- [           main] c.s.e.c.r.ISkyeyeClassEnumServiceRest    : [ISkyeyeClassEnumServiceRest#writeClassEnum] ---> POST http://skyeye-pro-dev/writeClassEnum HTTP/1.1
2025-07-30 19:06:01.977 DEBUG 442152 --- [           main] c.s.e.c.r.ISkyeyeClassEnumServiceRest    : [ISkyeyeClassEnumServiceRest#writeClassEnum] Content-Length: 29203
2025-07-30 19:06:01.977 DEBUG 442152 --- [           main] c.s.e.c.r.ISkyeyeClassEnumServiceRest    : [ISkyeyeClassEnumServiceRest#writeClassEnum] Content-Type: application/json
2025-07-30 19:06:01.977 DEBUG 442152 --- [           main] c.s.e.c.r.ISkyeyeClassEnumServiceRest    : [ISkyeyeClassEnumServiceRest#writeClassEnum] Content-Type: application/x-www-form-urlencoded; charset=UTF-8
2025-07-30 19:06:01.977 DEBUG 442152 --- [           main] c.s.e.c.r.ISkyeyeClassEnumServiceRest    : [ISkyeyeClassEnumServiceRest#writeClassEnum] options: feign.Request$Options@4e4715b4
2025-07-30 19:06:01.978 DEBUG 442152 --- [           main] c.s.e.c.r.ISkyeyeClassEnumServiceRest    : [ISkyeyeClassEnumServiceRest#writeClassEnum]
2025-07-30 19:06:01.978 DEBUG 442152 --- [           main] c.s.e.c.r.ISkyeyeClassEnumServiceRest    : [ISkyeyeClassEnumServiceRest#writeClassEnum] {"appId":"com.wzq.skyeye.crm","valueList":{"skyeye-crm#com.skyeye.documentary.classenum.CrmDocumentaryAuthEnum":[{"enumFiledName":"ADD","isDefault":false,"name":"新增","show":true,"id":"add"},{"enumFiledName":"EDIT","isDefault":false,"name":"编辑","show":true,"id":"edit"},{"enumFiledName":"DELETE","isDefault":false,"name":"删除","show":true,"id":"delete"}],"skyeye-crm#com.skyeye.attr.classenum.AttrSymbols":[{"enumFiledName":"LESS_THAN","isDefault":true,"name":"小于","show":true,"id":"lessThan","symbols":"<"},{"enumFiledName":"GREATER_THAN","isDefault":false,"name":"大于","show":true,"id":"greaterThan","symbols":">"},{"enumFiledName":"EQUAL_TO","isDefault":false,"name":"等于","show":true,"id":"equalTo","symbols":"=="},{"enumFiledName":"NOT_EQUAL","isDefault":false,"name":"不等于","show":true,"id":"notEqual","symbols":"!="},{"enumFiledName":"LESS_THAN_OR_EQUAL","isDefault":false,"name":"小于等于","show":true,"id":"lessThanOrEqual","symbols":"<="},{"enumFiledName":"GREATER_THAN_OR_EQUAL","isDefault":false,"name":"大于等于","show":true,"id":"greaterThanOrEqual","symbols":">="},{"enumFiledName":"CONTAIN","isDefault":false,"name":"包含","show":true,"id":"contain","symbols":"contain"}],"skyeye-crm#com.skyeye.follow.classenum.CrmFollowUpAuthEnum":[{"enumFiledName":"ADD","isDefault":false,"name":"新增","show":true,"id":"add"},{"enumFiledName":"EDIT","isDefault":false,"name":"编辑","show":true,"id":"edit"},{"enumFiledName":"DELETE","isDefault":false,"name":"删除","show":true,"id":"delete"}],"skyeye-crm#com.skyeye.common.enumeration.IsUsedEnum":[{"enumFiledName":"NOT_USED","isDefault":true,"color":"#FFC107","name":"未使用","show":true,"id":0},{"enumFiledName":"IN_USE","isDefault":false,"color":"#4CAF50","name":"已使用","show":true,"id":1}],"skyeye-crm#com.skyeye.common.enumeration.HttpMethodEnum":[{"enumFiledName":"GET_REQUEST","isDefault":true,"name":"GET请求","show":true,"id":"GET"},{"enumFiledName":"POST_REQUEST","isDefault":false,"name":"POST请求","show":true,"id":"POST"},{"enumFiledName":"DELETE_REQUEST","isDefault":false,"name":"DELETE请求","show":true,"id":"DELETE"},{"enumFiledName":"PUT_REQUEST","isDefault":false,"name":"PUT请求","show":true,"id":"PUT"}],"skyeye-crm#com.skyeye.common.enumeration.IsDefaultEnum":[{"enumFiledName":"IS_DEFAULT","isDefault":true,"color":"green","name":"是","show":true,"id":1},{"enumFiledName":"NOT_DEFAULT","isDefault":false,"color":"red","name":"否","show":true,"id":2}],"skyeye-crm#com.skyeye.common.enumeration.DeleteFlagEnum":[{"enumFiledName":"NOT_DELETE","isDefault":false,"name":"未删除","show":false,"id":0},{"enumFiledName":"DELETED","isDefault":false,"name":"删除","show":false,"id":1}],"skyeye-crm#com.skyeye.eve.flowable.classenum.FormSubType":[{"enumFiledName":"DRAFT","isDefault":false,"name":"保存草稿","show":false,"id":1},{"enumFiledName":"SUB_FLOWABLE","isDefault":false,"name":"提交到工作流","show":false,"id":2}],"skyeye-crm#com.skyeye.common.enumeration.AbnormalCheckworkType":[{"enumFiledName":"ABNORMAL_LEAVEEARLY","isDefault":false,"name":"早退","show":true,"id":"leaveEarly"},{"enumFiledName":"ABNORMAL_LATE","isDefault":false,"name":"迟到","show":true,"id":"late"},{"enumFiledName":"ABNORMAL_MINER","isDefault":false,"name":"旷工","show":true,"id":"miner"}],"skyeye-crm#com.skyeye.common.enumeration.FlowableChildStateEnum":[{"enumFiledName":"DRAFT","isDefault":true,"name":"未提交审批","show":true,"id":"0"},{"enumFiledName":"IN_EXAMINE","isDefault":false,"name":"审批中","show":true,"id":"1"},{"enumFiledName":"INSUFFICIENT","isDefault":false,"name":"不满足条件，审核失败","show":true,"id":"2"},{"enumFiledName":"ADEQUATE","isDefault":false,"name":"满足条件，审核成功","show":true,"id":"3"},{"enumFiledName":"REJECT","isDefault":false,"name":"驳回","show":true,"id":"4"}],"skyeye-crm#com.skyeye.common.enumeration.ApiAllUseState":[{"enumFiledName":"LOGIN_AND_AUTH","isDefault":true,"name":"需要登录并且赋权","show":true,"id":"1"},{"enumFiledName":"LOGIN_NOT_AUTH","isDefault":false,"name":"需要登录不需要赋权","show":true,"id":"2"},{"enumFiledName":"NOT","isDefault":false,"name":"既不需要登录也不需要赋权","show":true,"id":"0"}],"skyeye-crm#com.skyeye.contract.classenum.CrmContractFromType":[{"enumFiledName":"PURCHASE_REQUEST","isDefault":false,"name":"TODO 待定","show":true,"id":1}],"skyeye-crm#com.skyeye.common.enumeration.EnableEnum":[{"enumFiledName":"ENABLE_USING","isDefault":true,"color":"green","name":"启用","show":true,"id":1},{"enumFiledName":"DISABLE_USING","isDefault":false,"color":"red","name":"禁用","show":true,"id":2}],"skyeye-crm#com.skyeye.common.enumeration.SearchParamsConfigDataType":[{"enumFiledName":"INPUT","isDefault":true,"name":"文本","show":true,"id":"input"},{"enumFiledName":"DATE","isDefault":false,"name":"日期","show":true,"id":"date"},{"enumFiledName":"USER","isDefault":false,"name":"用户","show":true,"id":"user"},{"enumFiledName":"USER_STAFF","isDefault":false,"name":"员工","show":true,"id":"userStaff"},{"enumFiledName":"VIRTUAL_SELECT","isDefault":false,"name":"接口-下拉框","show":true,"id":"virtualSelect"},{"enumFiledName":"CONSTANT_SELECT","isDefault":false,"name":"常量-下拉框","show":true,"id":"constantSelect"}],"skyeye-crm#com.skyeye.common.enumeration.ScheduleDayObjectType":[{"enumFiledName":"PLAN","isDefault":false,"name":"任务计划","show":true,"id":1},{"enumFiledName":"PRO_TASK","isDefault":false,"name":"项目任务","show":true,"id":2}],"skyeye-crm#com.skyeye.common.enumeration.BloodTypeEnum":[{"enumFiledName":"A","isDefault":true,"name":"A型","show":true,"id":1},{"enumFiledName":"B","isDefault":false,"name":"B型","show":true,"id":2},{"enumFiledName":"AB","isDefault":false,"name":"AB型","show":true,"id":3},{"enumFiledName":"O","isDefault":false,"name":"O型","show":true,"id":4},{"enumFiledName":"OTHER","isDefault":false,"name":"其他","show":true,"id":5},{"enumFiledName":"UNDETERMINED","isDefault":false,"name":"未定","show":true,"id":6}],"skyeye-crm#com.skyeye.common.enumeration.UserQuitType":[{"enumFiledName":"AUTOMATIC_RESIGNATION","isDefault":false,"name":"自动离职","show":true,"id":1},{"enumFiledName":"retire","isDefault":false,"name":"退休","show":true,"id":2},{"enumFiledName":"DISEASE_WORDS","isDefault":false,"name":"病辞","show":true,"id":3},{"enumFiledName":"dismiss","isDefault":false,"name":"辞退","show":true,"id":4},{"enumFiledName":"resignation","isDefault":false,"name":"辞职","show":true,"id":5}],"skyeye-crm#com.skyeye.common.enumeration.UserStaffState":[{"enumFiledName":"ON_THE_JOB","isDefault":false,"color":"green","name":"在职(转正的员工)","show":true,"id":1},{"enumFiledName":"QUIT","isDefault":false,"color":"red","name":"离职","show":true,"id":2},{"enumFiledName":"PROBATION","isDefault":false,"color":"blue","name":"见习(用于实习生)","show":true,"id":3},{"enumFiledName":"PROBATION_PERIOD","isDefault":false,"color":"blue","name":"试用期(用于未转正的员工)","show":true,"id":4},{"enumFiledName":"RETIRE","isDefault":false,"color":"gray","name":"退休","show":true,"id":5}],"skyeye-crm#com.skyeye.common.enumeration.FlowableStateEnum":[{"enumFiledName":"DRAFT","isDefault":true,"color":"black","name":"草稿","show":true,"id":"draft"},{"enumFiledName":"IN_EXAMINE","isDefault":false,"color":"blue","name":"审核中","show":true,"id":"inExamine"},{"enumFiledName":"PASS","isDefault":false,"color":"green","name":"审核通过","show":true,"id":"pass"},{"enumFiledName":"REJECT","isDefault":false,"color":"red","name":"驳回","show":true,"id":"reject"},{"enumFiledName":"INVALID","isDefault":false,"color":"red","name":"作废","show":true,"id":"invalid"},{"enumFiledName":"REVOKE","isDefault":false,"color":"orange","name":"撤销","show":true,"id":"revoke"}],"skyeye-crm#com.skyeye.opportunity.classenum.CrmOpportunityStateEnum":[{"enumFiledName":"INITIAL_COMMUNICATION","isDefault":false,"name":"初期沟通","show":true,"id":"initialCommunication"},{"enumFiledName":"SCHEME_AND_QUOTATION","isDefault":false,"name":"方案与报价","show":true,"id":"schemeAndQuotation"},{"enumFiledName":"COMPETITION_AND_BIDDING","isDefault":false,"name":"竞争与投标","show":true,"id":"competitionAndBidding"},{"enumFiledName":"BUSINESS_NEGOTIATION","isDefault":false,"name":"商务谈判","show":true,"id":"businessNegotiation"},{"enumFiledName":"STRIKE_BARGAIN","isDefault":false,"name":"成交","show":true,"id":"strikeBargain"},{"enumFiledName":"LOST_ORDER","isDefault":false,"name":"丢单","show":true,"id":"lostOrder"},{"enumFiledName":"LAY_ASIDE","isDefault":false,"name":"搁置","show":true,"id":"layAside"},{"enumFiledName":"DRAFT","isDefault":true,"color":"black","name":"草稿","show":true,"id":"draft"},{"enumFiledName":"IN_EXAMINE","isDefault":false,"color":"blue","name":"审核中","show":true,"id":"inExamine"},{"enumFiledName":"PASS","isDefault":false,"color":"green","name":"审核通过","show":true,"id":"pass"},{"enumFiledName":"REJECT","isDefault":false,"color":"red","name":"驳回","show":true,"id":"reject"},{"enumFiledName":"INVALID","isDefault":false,"color":"red","name":"作废","show":true,"id":"invalid"},{"enumFiledName":"REVOKE","isDefault":false,"color":"orange","name":"撤销","show":true,"id":"revoke"}],"skyeye-crm#com.skyeye.common.enumeration.SearchOperator":[{"enumFiledName":"LESS_THAN","isDefault":true,"name":"大于","show":true,"id":">","sql":" AND DATE_FORMAT(表.字段, '%Y-%m-%d') > '?'"},{"enumFiledName":"GREATER_THAN","isDefault":false,"name":"小于","show":true,"id":"<","sql":" AND DATE_FORMAT(表.字段,'%Y-%m-%d') < '?'"},{"enumFiledName":"EQUAL","isDefault":true,"name":"等于","show":true,"id":"=","sql":" AND DATE_FORMAT(表.字段,'%Y-%m-%d') = '?'"},{"enumFiledName":"NOT_EQUAL","isDefault":false,"name":"不等于","show":true,"id":"!=","sql":" AND DATE_FORMAT(表.字段,'%Y-%m-%d') != '?'"},{"enumFiledName":"LESS_THAN_OR_EQUAL","isDefault":false,"name":"小于等于","show":true,"id":"<=","sql":" AND DATE_FORMAT(表.字段,'%Y-%m-%d') <= '?'"},{"enumFiledName":"GREATER_THAN_OR_EQUAL","isDefault":false,"name":"大于等于","show":true,"id":">=","sql":" AND DATE_FORMAT(表.字段,'%Y-%m-%d') >= '?'"},{"enumFiledName":"IN","isDefault":false,"name":"包含","show":true,"id":"in","sql":""},{"enumFiledName":"LIKE","isDefault":false,"name":"相似","show":true,"id":"like","sql":" AND 表.字段 LIKE '%?%'"},{"enumFiledName":"NOT_LIKE","isDefault":false,"name":"不相似","show":true,"id":"not like","sql":" 表.字段 NOT LIKE '%?%'"},{"enumFiledName":"START_WITH","isDefault":false,"name":"以...开头","show":true,"id":"start with","sql":" AND 表.字段 LIKE '?%'"},{"enumFiledName":"END_WITH","isDefault":false,"name":"以...结束","show":true,"id":"end with","sql":" AND 表.字段 LIKE '%?'"},{"enumFiledName":"BETWEEN","isDefault":false,"name":"区间","show":true,"id":"between","sql":" AND DATE_FORMAT(表.字段, '%Y-%m-%d') >= '?' AND DATE_FORMAT(表.字段, '%Y-%m-%d') <= '?'"}],"skyeye-crm#com.skyeye.common.enumeration.IDCardType":[{"enumFiledName":"ID_CARD","isDefault":true,"name":"居民身份证","show":true,"id":1},{"enumFiledName":"OTHER","isDefault":false,"name":"其他","show":true,"id":10}],"skyeye-crm#com.skyeye.common.enumeration.RequestType":[{"enumFiledName":"PC","isDefault":false,"name":"PC端请求","show":false,"id":"1"},{"enumFiledName":"APP","isDefault":false,"name":"手机端请求","show":false,"id":"2"}],"skyeye-crm#com.skyeye.common.enumeration.QRCodeLinkType":[{"enumFiledName":"SUBJECT_CLASSES","isDefault":true,"name":"学校模块-课程与班级关系","show":true,"id":"subjectClasses"},{"enumFiledName":"STUDENT_CHECKWORK","isDefault":true,"name":"学校模块-考勤签到","show":true,"id":"studentCheckwork"}],"skyeye-crm#com.skyeye.opportunity.classenum.CrmOpportunityAuthEnum":[{"enumFiledName":"LIST","isDefault":false,"name":"查看列表","show":true,"id":"list"},{"enumFiledName":"ADD","isDefault":false,"name":"新增","show":true,"id":"add"},{"enumFiledName":"EDIT","isDefault":false,"name":"编辑","show":true,"id":"edit"},{"enumFiledName":"DELETE","isDefault":false,"name":"删除","show":true,"id":"delete"},{"enumFiledName":"REVOKE","isDefault":false,"name":"撤销","show":true,"id":"revoke"},{"enumFiledName":"INVALID","isDefault":false,"name":"作废","show":true,"id":"invalid"},{"enumFiledName":"SUBMIT_TO_APPROVAL","isDefault":false,"name":"提交审批","show":true,"id":"submitToApproval"},{"enumFiledName":"CONMUNICATE","isDefault":false,"name":"初期沟通","show":true,"id":"conmunicate"},{"enumFiledName":"QUOTED_PRICE","isDefault":false,"name":"方案与报价","show":true,"id":"quotedPrice"},{"enumFiledName":"TENDER","isDefault":false,"name":"竞争与投标","show":true,"id":"tender"},{"enumFiledName":"NEGOTIATE","isDefault":false,"name":"商务谈判","show":true,"id":"negotiate"},{"enumFiledName":"TURNOVER","isDefault":false,"name":"成交","show":true,"id":"turnover"},{"enumFiledName":"LOSING_TABLE","isDefault":false,"name":"丢单","show":true,"id":"losingTable"},{"enumFiledName":"LAY_ASIDE","isDefault":false,"name":"搁置","show":true,"id":"layAside"}],"skyeye-crm#com.skyeye.common.enumeration.ApplicableObjectsType":[{"enumFiledName":"STAFF","isDefault":true,"name":"员工","show":true,"id":1},{"enumFiledName":"DEPARTMENT","isDefault":false,"name":"部门","show":true,"id":2},{"enumFiledName":"COMPANY","isDefault":false,"name":"企业","show":true,"id":3}],"skyeye-crm#com.skyeye.contract.classenum.CrmContractStateEnum":[{"enumFiledName":"EXECUTING","isDefault":false,"name":"执行中","show":true,"id":"executing"},{"enumFiledName":"CLOSE","isDefault":false,"name":"关闭","show":true,"id":"close"},{"enumFiledName":"LAY_ASIDE","isDefault":false,"name":"搁置","show":true,"id":"layAside"},{"enumFiledName":"DRAFT","isDefault":true,"color":"black","name":"草稿","show":true,"id":"draft"},{"enumFiledName":"IN_EXAMINE","isDefault":false,"color":"blue","name":"审核中","show":true,"id":"inExamine"},{"enumFiledName":"PASS","isDefault":false,"color":"green","name":"审核通过","show":true,"id":"pass"},{"enumFiledName":"REJECT","isDefault":false,"color":"red","name":"驳回","show":true,"id":"reject"},{"enumFiledName":"INVALID","isDefault":false,"color":"red","name":"作废","show":true,"id":"invalid"},{"enumFiledName":"REVOKE","isDefault":false,"color":"orange","name":"撤销","show":true,"id":"revoke"}],"skyeye-crm#com.skyeye.invoice.classenum.CrmInvoiceHeaderAuthEnum":[{"enumFiledName":"ADD","isDefault":false,"name":"新增","show":true,"id":"add"},{"enumFiledName":"EDIT","isDefault":false,"name":"编辑","show":true,"id":"edit"},{"enumFiledName":"DELETE","isDefault":false,"name":"删除","show":true,"id":"delete"}],"skyeye-crm#com.skyeye.payment.classenum.CrmPaymentCollectionAuthEnum":[{"enumFiledName":"LIST","isDefault":false,"name":"查看列表","show":true,"id":"list"},{"enumFiledName":"ADD","isDefault":false,"name":"新增","show":true,"id":"add"},{"enumFiledName":"EDIT","isDefault":false,"name":"编辑","show":true,"id":"edit"},{"enumFiledName":"DELETE","isDefault":false,"name":"删除","show":true,"id":"delete"},{"enumFiledName":"REVOKE","isDefault":false,"name":"撤销","show":true,"id":"revoke"},{"enumFiledName":"INVALID","isDefault":false,"name":"作废","show":true,"id":"invalid"},{"enumFiledName":"SUBMIT_TO_APPROVAL","isDefault":false,"name":"提交审批","show":true,"id":"submitToApproval"}],"skyeye-crm#com.skyeye.common.enumeration.CorrespondentAllEnterEnum":[{"enumFiledName":"SUPPLIER","isDefault":true,"name":"供应商","show":true,"id":"com.skyeye.supplier.service.impl.SupplierServiceImpl"},{"enumFiledName":"CUSTOM","isDefault":false,"name":"客户","show":true,"id":"com.skyeye.customer.service.impl.CustomerServiceImpl"},{"enumFiledName":"MEMBER","isDefault":false,"name":"会员","show":true,"id":"com.skyeye.service.impl.MemberServiceImpl"}],"skyeye-crm#com.skyeye.common.enumeration.SessionStatus":[{"enumFiledName":"APINOTFOUND","isDefault":true,"name":"API不存在","show":true},{"enumFiledName":"TIMEOUT","isDefault":false,"name":"会话超时","show":true},{"enumFiledName":"NOAUTHPOINT","isDefault":false,"name":"无权限","show":true}],"skyeye-crm#com.skyeye.common.enumeration.SexEnum":[{"enumFiledName":"KEEP_BACK","isDefault":true,"name":"保密","show":true,"id":0},{"enumFiledName":"MAN","isDefault":false,"name":"男","show":true,"id":1},{"enumFiledName":"WOMAN","isDefault":false,"name":"女","show":true,"id":2}],"skyeye-crm#com.skyeye.common.enumeration.SmsSceneEnum":[{"enumFiledName":"LOGIN","isDefault":false,"name":"user-sms-login","show":true,"remark":"用户 - 手机号登陆","id":1},{"enumFiledName":"UPDATE_MOBILE","isDefault":false,"name":"user-update-mobile","show":true,"remark":"用户 - 修改绑定的手机","id":2},{"enumFiledName":"UPDATE_PASSWORD","isDefault":false,"name":"user-update-password","show":true,"remark":"用户 - 修改密码","id":3},{"enumFiledName":"RESET_PASSWORD","isDefault":false,"name":"user-reset-password","show":true,"remark":"用户 - 忘记密码","id":4}],"skyeye-crm#com.skyeye.common.enumeration.CheckDayType":[{"enumFiledName":"DAY_IS_PERSONAL","isDefault":false,"color":"#63B8FF","name":"个人","show":true,"className":"","id":1},{"enumFiledName":"DAY_IS_WORK","isDefault":false,"color":"#CD69C9","name":"工作","show":true,"className":"","id":2},{"enumFiledName":"DAY_IS_HOLIDAY","isDefault":false,"color":"#54FF9F","name":"节假日","show":true,"className":"xiu","id":3},{"enumFiledName":"DAY_IS_BIRTHDAY","isDefault":false,"color":"#FF0000","name":"生日","show":true,"className":"","id":4},{"enumFiledName":"DAY_IS_CUSTOM","isDefault":false,"color":"#ADADAD","name":"自定义","show":true,"className":"","id":5},{"enumFiledName":"DAY_IS_WORKING","isDefault":false,"color":"","name":"工作日","show":true,"className":"work-time","id":6},{"enumFiledName":"DAY_IS_WORK_OVERTIME","isDefault":false,"color":"","name":"加班","show":true,"className":"work-overtime","id":7},{"enumFiledName":"DAY_IS_LEAVE","isDefault":false,"color":"","name":"请假","show":true,"className":"leave","id":8},{"enumFiledName":"DAY_IS_BUSINESS_TRAVEL","isDefault":false,"color":"","name":"出差","show":true,"className":"business-travel","id":9}],"skyeye-crm#com.skyeye.common.enumeration.TenantEnum":[{"enumFiledName":"STRONG_ISOLATION","isDefault":false,"name":"强制隔离，只查询当前租户的数据","show":true,"id":"strongIsolation"},{"enumFiledName":"WEAK_ISOLATION","isDefault":false,"name":"弱隔离，查询当前租户和平台的数据","show":true,"id":"weakIsolation"},{"enumFiledName":"NO_ISOLATION","isDefault":false,"name":"不做隔离","show":true,"id":"noIsolation"},{"enumFiledName":"PLATE","isDefault":false,"name":"只有平台的账号可以查看","show":true,"id":"plate"}],"skyeye-crm#com.skyeye.contract.classenum.CrmContractChildStateEnum":[{"enumFiledName":"PENDING_ORDER","isDefault":false,"name":"待下达订单","show":true,"id":"pendingOrder"},{"enumFiledName":"PARTIAL_RELEASE","isDefault":false,"name":"部分下达","show":true,"id":"partialRelease"},{"enumFiledName":"ALL_ISSUED","isDefault":false,"name":"全部下达","show":true,"id":"allIssued"}],"skyeye-crm#com.skyeye.common.enumeration.HealthStatus":[{"enumFiledName":"HEALTH","isDefault":true,"name":"健康","show":true,"id":1},{"enumFiledName":"GOOD","isDefault":false,"name":"良好","show":true,"id":2},{"enumFiledName":"GENERAL","isDefault":false,"name":"一般","show":true,"id":3},{"enumFiledName":"WEAK","isDefault":false,"name":"较弱","show":true,"id":4},{"enumFiledName":"OTHER","isDefault":false,"name":"其他","show":true,"id":5}],"skyeye-crm#com.skyeye.common.enumeration.WinterVacationType":[{"enumFiledName":"ONE_YEAR","isDefault":false,"min":0,"max":1,"name":"1年以下","show":true,"id":1},{"enumFiledName":"ONE_THREE_YEAR","isDefault":false,"min":1,"max":3,"name":"1年 ~ 3年","show":true,"id":2},{"enumFiledName":"THREE_FIVE_YEAR","isDefault":false,"min":3,"max":5,"name":"3年 ~ 5年","show":true,"id":3},{"enumFiledName":"FIVE_EIGHT_YEAR","isDefault":false,"min":5,"max":8,"name":"5年 ~ 8年","show":true,"id":4},{"enumFiledName":"EIGHT_YEAR","isDefault":false,"min":8,"max":100,"name":"8年以上","show":true,"id":5}],"skyeye-crm#com.skyeye.common.enumeration.WhetherEnum":[{"enumFiledName":"ENABLE_USING","isDefault":true,"name":"是","show":true,"id":1},{"enumFiledName":"DISABLE_USING","isDefault":false,"name":"否","show":true,"id":0}],"skyeye-crm#com.skyeye.common.enumeration.OvertimeSettlementType":[{"enumFiledName":"SINGLE_SALARY_SETTLEMENT","isDefault":true,"name":"单倍薪资结算","show":true,"id":1},{"enumFiledName":"ONE_POINT_FIVE_SALARY_SETTLEMENT","isDefault":false,"name":"1.5倍薪资结算","show":true,"id":2},{"enumFiledName":"DOUBLE_SALARY_SETTLEMENT","isDefault":false,"name":"双倍薪资结算","show":true,"id":3},{"enumFiledName":"COMPENSATORY_LEAVE_SETTLEMENT","isDefault":false,"name":"补休结算","show":true,"id":6}],"skyeye-crm#com.skyeye.common.enumeration.CorrespondentEnterEnum":[{"enumFiledName":"SUPPLIER","isDefault":true,"name":"供应商","show":true,"id":"com.skyeye.supplier.service.impl.SupplierServiceImpl"},{"enumFiledName":"CUSTOM","isDefault":false,"name":"客户","show":true,"id":"com.skyeye.customer.service.impl.CustomerServiceImpl"}],"skyeye-crm#com.skyeye.common.enumeration.ShopMaterialDeliveryMethod":[{"enumFiledName":"DEFAULT_SET","isDefault":false,"name":"快递发货","show":true,"id":1},{"enumFiledName":"SINGLE_SET","isDefault":false,"name":"用户自提","show":true,"id":2},{"enumFiledName":"LOCAL_SET","isDefault":false,"name":"同城配送","show":true,"id":3}],"skyeye-crm#com.skyeye.common.enumeration.NoticeUserMessageTypeEnum":[{"enumFiledName":"SCHEDULE_MESSAGE","isDefault":true,"name":"日程消息","show":true,"id":1},{"enumFiledName":"PLAN_REMINDER","isDefault":false,"name":"计划提醒","show":true,"id":2},{"enumFiledName":"WORK_ORDER_REMINDER","isDefault":false,"name":"工单派工提醒","show":true,"id":3}],"skyeye-crm#com.skyeye.common.enumeration.WeekDay":[{"enumFiledName":"MON","isDefault":false,"name":"星期一","show":true,"id":1},{"enumFiledName":"TUE","isDefault":false,"name":"星期二","show":true,"id":2},{"enumFiledName":"WED","isDefault":false,"name":"星期三","show":true,"id":3},{"enumFiledName":"THU","isDefault":false,"name":"星期四","show":true,"id":4},{"enumFiledName":"FRI","isDefault":false,"name":"星期五","show":true,"id":5},{"enumFiledName":"SAT","isDefault":false,"name":"星期六","show":true,"id":6},{"enumFiledName":"SUN","isDefault":false,"name":"星期日","show":true,"id":7}],"skyeye-crm#com.skyeye.common.enumeration.VerificationParamsEnum":[{"enumFiledName":"REQUIRED","isDefault":false,"formerRequirement":"required","method":"isBlank","name":"非空限制","show":true,"id":"required","regular":"","desc":"参数非空校验","resultMsg":"不能为空"},{"enumFiledName":"NUM","isDefault":false,"formerRequirement":"number","method":"regularCheck","name":"数字限制","show":true,"id":"num","regular":"^(\\-|\\+)?\\d+(\\.\\d+)?$","desc":"数字校验","resultMsg":"数字类型不正确"},{"enumFiledName":"DATE","isDefault":false,"formerRequirement":"date","method":"isDate","name":"日期限制","show":true,"id":"date","regular":"","desc":"时间校验,格式：yyyy-MM-dd HH:mm:ss","resultMsg":"时间类型不正确"},{"enumFiledName":"EMAIL","isDefault":false,"formerRequirement":"email","method":"regularCheck","name":"邮箱限制","show":true,"id":"email","regular":"^([\\w-\\.]+)@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.)|(([\\w-]+\\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\\]?)$","desc":"邮箱校验","resultMsg":"邮箱类型不正确"},{"enumFiledName":"IDCARD","isDefault":false,"formerRequirement":"identity","method":"regularCheck","name":"身份证限制","show":true,"id":"idcard","regular":"(^\\d{18}$)|(^\\d{15}$)|(^\\d{17}(x|X|\\d)$)","desc":"证件号校验","resultMsg":"证件号类型不正确"},{"enumFiledName":"PHONE","isDefault":false,"formerRequirement":"phone","method":"regularCheck","name":"手机号限制","show":true,"id":"phone","regular":"^((13[0-9])|(15[^4])|(16[0-9])|(19[0-9])|(18[0-9])|(17[0-8])|(147))\\d{8}$","desc":"手机号校验","resultMsg":"手机号类型不正确"},{"enumFiledName":"URL","isDefault":false,"formerRequirement":"url","method":"regularCheck","name":"链接格式限制","show":true,"id":"url","regular":"http(s)?://([\\w-]+\\.)+[\\w-]+(/[\\w- ./?%&=]*)?","desc":"url校验","resultMsg":"url类型不正确"},{"enumFiledName":"IP","isDefault":false,"formerRequirement":"","method":"regularCheck","name":"ip校验","show":false,"id":"ip","regular":"^(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)$","desc":"ip校验","resultMsg":"ip类型不正确"},{"enumFiledName":"POSTCODE","isDefault":false,"formerRequirement":"postcode","method":"regularCheck","name":"邮编限制","show":true,"id":"postcode","regular":"^\\d{6}$","desc":"国内邮编校验","resultMsg":"国内邮编类型不正确"},{"enumFiledName":"DOUBLE","isDefault":false,"formerRequirement":"double","method":"regularCheck","name":"金钱校验","show":true,"id":"double","regular":"^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){0,6})?$","desc":"金钱校验","resultMsg":"小数格式类型不正确"},{"enumFiledName":"PERCENTAGE","isDefault":false,"formerRequirement":"percentage","method":"regularCheck","name":"百分比小数校验","show":true,"id":"percentage","regular":"^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){0,2})?$","desc":"百分比小数校验","resultMsg":"百分比不正确"},{"enumFiledName":"VINCODE","isDefault":false,"formerRequirement":"vincode","method":"regularCheck","name":"VIN码校验","show":true,"id":"vincode","regular":"^(?![^A-Z]+$)(?![^0-9]+$)[1-9WTJSKLVRYZ]{1}[A-HJ-NPR-Z0-9]{16}$","desc":"VIN码校验","resultMsg":"VIN码不正确"},{"enumFiledName":"PLATENO","isDefault":false,"formerRequirement":"plateno","method":"regularCheck","name":"车牌号校验","show":true,"id":"plateno","regular":"^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$","desc":"车牌号校验","resultMsg":"车牌号不正确"},{"enumFiledName":"JSON","isDefault":false,"formerRequirement":"","method":"isJson","name":"json字符串校验","show":false,"id":"json","regular":"","desc":"json字符串校验","resultMsg":"参数类型非json"}],"skyeye-crm#com.skyeye.common.enumeration.WeekTypeEnum":[{"enumFiledName":"ODD_WEEKS","isDefault":false,"name":"单周","show":false,"id":0},{"enumFiledName":"BIWEEKLY","isDefault":false,"name":"双周","show":false,"id":1}],"skyeye-crm#com.skyeye.common.enumeration.PayTypeEnum":[{"enumFiledName":"CASH","isDefault":true,"name":"现金","show":true,"id":1},{"enumFiledName":"ACCOUNTING","isDefault":false,"name":"记账","show":true,"id":2},{"enumFiledName":"OTHER","isDefault":false,"name":"其他","show":true,"id":3}],"skyeye-crm#com.skyeye.common.enumeration.RegistrationType":[{"enumFiledName":"AGRICULTURAL","isDefault":true,"name":"农业户口","show":true,"id":1},{"enumFiledName":"NON_AGRICULTURAL","isDefault":false,"name":"非农业户口","show":true,"id":2}],"skyeye-crm#com.skyeye.contract.classenum.CrmContractAuthEnum":[{"enumFiledName":"LIST","isDefault":false,"name":"查看列表","show":true,"id":"list"},{"enumFiledName":"ADD","isDefault":false,"name":"新增","show":true,"id":"add"},{"enumFiledName":"EDIT","isDefault":false,"name":"编辑","show":true,"id":"edit"},{"enumFiledName":"DELETE","isDefault":false,"name":"删除","show":true,"id":"delete"},{"enumFiledName":"REVOKE","isDefault":false,"name":"撤销","show":true,"id":"revoke"},{"enumFiledName":"INVALID","isDefault":false,"name":"作废","show":true,"id":"invalid"},{"enumFiledName":"SUBMIT_TO_APPROVAL","isDefault":false,"name":"提交审批","show":true,"id":"submitToApproval"},{"enumFiledName":"PERFORM","isDefault":false,"name":"执行","show":true,"id":"perform"},{"enumFiledName":"CLOSE","isDefault":false,"name":"关闭","show":true,"id":"close"},{"enumFiledName":"LAY_ASIDE","isDefault":false,"name":"搁置","show":true,"id":"layAside"},{"enumFiledName":"RECOVERY","isDefault":false,"name":"恢复","show":true,"id":"recovery"}],"skyeye-crm#com.skyeye.invoice.classenum.CrmInvoiceAuthEnum":[{"enumFiledName":"LIST","isDefault":false,"name":"查看列表","show":true,"id":"list"},{"enumFiledName":"ADD","isDefault":false,"name":"新增","show":true,"id":"add"},{"enumFiledName":"EDIT","isDefault":false,"name":"编辑","show":true,"id":"edit"},{"enumFiledName":"DELETE","isDefault":false,"name":"删除","show":true,"id":"delete"},{"enumFiledName":"REVOKE","isDefault":false,"name":"撤销","show":true,"id":"revoke"},{"enumFiledName":"INVALID","isDefault":false,"name":"作废","show":true,"id":"invalid"},{"enumFiledName":"SUBMIT_TO_APPROVAL","isDefault":false,"name":"提交审批","show":true,"id":"submitToApproval"}]}}
2025-07-30 19:06:01.979 DEBUG 442152 --- [           main] c.s.e.c.r.ISkyeyeClassEnumServiceRest    : [ISkyeyeClassEnumServiceRest#writeClassEnum] ---> END HTTP (29203-byte body)
2025-07-30 19:06:02.039  WARN 442152 --- [           main] o.s.c.l.c.RoundRobinLoadBalancer         : No servers available for service: skyeye-pro-dev
2025-07-30 19:06:02.039  WARN 442152 --- [           main] .s.c.o.l.FeignBlockingLoadBalancerClient : Load balancer does not contain an instance for the service skyeye-pro-dev
2025-07-30 19:06:02.041 DEBUG 442152 --- [           main] c.s.e.c.r.ISkyeyeClassEnumServiceRest    : [ISkyeyeClassEnumServiceRest#writeClassEnum] <--- HTTP/1.1 503 (61ms)
2025-07-30 19:06:02.041 DEBUG 442152 --- [           main] c.s.e.c.r.ISkyeyeClassEnumServiceRest    : [ISkyeyeClassEnumServiceRest#writeClassEnum]
2025-07-30 19:06:02.041 DEBUG 442152 --- [           main] c.s.e.c.r.ISkyeyeClassEnumServiceRest    : [ISkyeyeClassEnumServiceRest#writeClassEnum] Load balancer does not contain an instance for the service skyeye-pro-dev
2025-07-30 19:06:02.041 DEBUG 442152 --- [           main] c.s.e.c.r.ISkyeyeClassEnumServiceRest    : [ISkyeyeClassEnumServiceRest#writeClassEnum] <--- END HTTP (73-byte body)
2025-07-30 19:06:02.043  WARN 442152 --- [           main] c.s.c.c.ExecuteFeignClient               : ExecuteFeignClient is error, {}

feign.FeignException$ServiceUnavailable: [503] during [POST] to [http://skyeye-pro-dev/writeClassEnum] [ISkyeyeClassEnumServiceRest#writeClassEnum(Map,Options)]: [Load balancer does not contain an instance for the service skyeye-pro-dev]
	at feign.FeignException.serverErrorStatus(FeignException.java:256) ~[feign-core-11.10.jar:?]
	at feign.FeignException.errorStatus(FeignException.java:197) ~[feign-core-11.10.jar:?]
	at feign.FeignException.errorStatus(FeignException.java:185) ~[feign-core-11.10.jar:?]
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92) ~[feign-core-11.10.jar:?]
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:98) ~[feign-core-11.10.jar:?]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:141) ~[feign-core-11.10.jar:?]
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:91) ~[feign-core-11.10.jar:?]
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100) ~[feign-core-11.10.jar:?]
	at jdk.proxy2.$Proxy272.writeClassEnum(Unknown Source) ~[?:?]
	at com.skyeye.common.base.listener.impl.LoadClassEnumListener.lambda$run$0(LoadClassEnumListener.java:76) ~[classes/:?]
	at com.skyeye.common.client.ExecuteFeignClient.get(ExecuteFeignClient.java:31) ~[classes/:?]
	at com.skyeye.common.base.listener.impl.LoadClassEnumListener.run(LoadClassEnumListener.java:76) ~[classes/:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at com.skyeye.common.base.listener.SkyeyeStartListener.run(SkyeyeStartListener.java:62) ~[classes/:?]
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746) ~[spring-boot-2.7.18.jar:2.7.18]
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183) [?:?]
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:357) [?:?]
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:510) [?:?]
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499) [?:?]
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150) [?:?]
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173) [?:?]
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234) [?:?]
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596) [?:?]
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289) [spring-boot-2.7.18.jar:2.7.18]
	at com.CrmApplication.main(CrmApplication.java:27) [classes/:?]

2025-07-30 19:06:02.047  INFO 442152 --- [           main] ConditionEvaluationReportLoggingListener :

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-30 19:06:02.049  INFO 442152 --- [           main] o.q.c.QuartzScheduler                    : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 19:06:02.058  INFO 442152 --- [           main] c.s.c.f.SessionFilter                    : system is destory!!!!
2025-07-30 19:06:06.845  INFO 442152 --- [lientSelector_1] RocketmqRemoting                         : closeChannel: close the connection to remote address[*************:9876] result: true
2025-07-30 19:06:06.845  INFO 442152 --- [lientSelector_1] RocketmqRemoting                         : closeChannel: close the connection to remote address[**************:10911] result: true
2025-07-30 19:06:06.845  INFO 442152 --- [           main] o.s.s.q.SchedulerFactoryBean             : Shutting down Quartz Scheduler
2025-07-30 19:06:06.846  INFO 442152 --- [           main] o.q.c.QuartzScheduler                    : Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-30 19:06:06.846  INFO 442152 --- [           main] o.q.c.QuartzScheduler                    : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 19:06:06.846  INFO 442152 --- [           main] o.q.c.QuartzScheduler                    : Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-30 19:06:06.846  INFO 442152 --- [           main] c.a.c.n.r.NacosServiceRegistry           : De-registering from Nacos Server now...
2025-07-30 19:06:06.860  INFO 442152 --- [           main] c.a.c.n.r.NacosServiceRegistry           : De-registration finished.
2025-07-30 19:06:07.166  INFO 442152 --- [      Thread-29] c.x.j.c.s.EmbedServer                    : >>>>>>>>>>> xxl-job remoting server stop.
2025-07-30 19:06:07.173  INFO 442152 --- [rRegistryThread] c.x.j.c.t.ExecutorRegistryThread         : >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='xxl-job-skyeye-crm-dev-executor', registryValue='http://26.44.187.191:18102/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-07-30 19:06:07.173  INFO 442152 --- [rRegistryThread] c.x.j.c.t.ExecutorRegistryThread         : >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-07-30 19:06:07.174  INFO 442152 --- [           main] c.x.j.c.s.EmbedServer                    : >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-30 19:06:07.174  INFO 442152 --- [FileCleanThread] c.x.j.c.t.JobLogFileCleanThread          : >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-07-30 19:06:07.174  INFO 442152 --- [rCallbackThread] c.x.j.c.t.TriggerCallbackThread          : >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-07-30 19:06:07.174  INFO 442152 --- [      Thread-28] c.x.j.c.t.TriggerCallbackThread          : >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-07-30 19:06:07.179  WARN 442152 --- [           main] s.c.a.AnnotationConfigApplicationContext : Exception thrown from ApplicationListener handling ContextClosedEvent

org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'registrationListener': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1168) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.getTargetBean(ApplicationListenerMethodAdapter.java:371) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:336) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:229) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:166) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:435) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:99) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1120) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1086) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:94) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:82) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.bootstrap.BootstrapApplicationListener$CloseContextOnFailureApplicationListener.onApplicationEvent(BootstrapApplicationListener.java:504) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.context.event.EventPublishingRunListener.failed(EventPublishingRunListener.java:124) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.callFailedListener(SpringApplicationRunListeners.java:96) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.lambda$failed$7(SpringApplicationRunListeners.java:87) ~[spring-boot-2.7.18.jar:2.7.18]
	at java.util.ArrayList.forEach(ArrayList.java:1511) [?:?]
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:120) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.failed(SpringApplicationRunListeners.java:86) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.handleRunFailure(SpringApplication.java:778) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289) [spring-boot-2.7.18.jar:2.7.18]
	at com.CrmApplication.main(CrmApplication.java:27) [classes/:?]

2025-07-30 19:06:07.181  WARN 442152 --- [           main] s.c.a.AnnotationConfigApplicationContext : Exception thrown from ApplicationListener handling ContextClosedEvent

org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'registrationListener': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1168) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.getTargetBean(ApplicationListenerMethodAdapter.java:371) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:336) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:229) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:166) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:435) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:99) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1120) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1086) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:94) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:82) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.bootstrap.BootstrapApplicationListener$CloseContextOnFailureApplicationListener.onApplicationEvent(BootstrapApplicationListener.java:504) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.context.event.EventPublishingRunListener.failed(EventPublishingRunListener.java:124) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.callFailedListener(SpringApplicationRunListeners.java:96) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.lambda$failed$7(SpringApplicationRunListeners.java:87) ~[spring-boot-2.7.18.jar:2.7.18]
	at java.util.ArrayList.forEach(ArrayList.java:1511) [?:?]
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:120) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.failed(SpringApplicationRunListeners.java:86) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.handleRunFailure(SpringApplication.java:778) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289) [spring-boot-2.7.18.jar:2.7.18]
	at com.CrmApplication.main(CrmApplication.java:27) [classes/:?]

2025-07-30 19:06:07.181  WARN 442152 --- [           main] s.c.a.AnnotationConfigApplicationContext : Exception thrown from ApplicationListener handling ContextClosedEvent

org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'registrationListener': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1168) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.getTargetBean(ApplicationListenerMethodAdapter.java:371) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:336) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:229) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:166) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:435) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:99) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1120) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1086) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:94) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:82) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.bootstrap.BootstrapApplicationListener$CloseContextOnFailureApplicationListener.onApplicationEvent(BootstrapApplicationListener.java:504) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.context.event.EventPublishingRunListener.failed(EventPublishingRunListener.java:124) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.callFailedListener(SpringApplicationRunListeners.java:96) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.lambda$failed$7(SpringApplicationRunListeners.java:87) ~[spring-boot-2.7.18.jar:2.7.18]
	at java.util.ArrayList.forEach(ArrayList.java:1511) [?:?]
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:120) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.failed(SpringApplicationRunListeners.java:86) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.handleRunFailure(SpringApplication.java:778) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289) [spring-boot-2.7.18.jar:2.7.18]
	at com.CrmApplication.main(CrmApplication.java:27) [classes/:?]

2025-07-30 19:06:07.182  WARN 442152 --- [           main] s.c.a.AnnotationConfigApplicationContext : Exception thrown from ApplicationListener handling ContextClosedEvent

org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'registrationListener': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1168) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.getTargetBean(ApplicationListenerMethodAdapter.java:371) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:336) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:229) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:166) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:435) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:99) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1120) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1086) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:94) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:82) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.bootstrap.BootstrapApplicationListener$CloseContextOnFailureApplicationListener.onApplicationEvent(BootstrapApplicationListener.java:504) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.context.event.EventPublishingRunListener.failed(EventPublishingRunListener.java:124) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.callFailedListener(SpringApplicationRunListeners.java:96) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.lambda$failed$7(SpringApplicationRunListeners.java:87) ~[spring-boot-2.7.18.jar:2.7.18]
	at java.util.ArrayList.forEach(ArrayList.java:1511) [?:?]
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:120) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.failed(SpringApplicationRunListeners.java:86) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.handleRunFailure(SpringApplication.java:778) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289) [spring-boot-2.7.18.jar:2.7.18]
	at com.CrmApplication.main(CrmApplication.java:27) [classes/:?]

2025-07-30 19:06:07.182  WARN 442152 --- [           main] s.c.a.AnnotationConfigApplicationContext : Exception thrown from ApplicationListener handling ContextClosedEvent

org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'registrationListener': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1168) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.getTargetBean(ApplicationListenerMethodAdapter.java:371) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:336) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:229) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:166) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:435) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:99) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1120) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1086) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:94) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:82) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.bootstrap.BootstrapApplicationListener$CloseContextOnFailureApplicationListener.onApplicationEvent(BootstrapApplicationListener.java:504) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.context.event.EventPublishingRunListener.failed(EventPublishingRunListener.java:124) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.callFailedListener(SpringApplicationRunListeners.java:96) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.lambda$failed$7(SpringApplicationRunListeners.java:87) ~[spring-boot-2.7.18.jar:2.7.18]
	at java.util.ArrayList.forEach(ArrayList.java:1511) [?:?]
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:120) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.failed(SpringApplicationRunListeners.java:86) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.handleRunFailure(SpringApplication.java:778) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289) [spring-boot-2.7.18.jar:2.7.18]
	at com.CrmApplication.main(CrmApplication.java:27) [classes/:?]

2025-07-30 19:06:07.182  WARN 442152 --- [           main] s.c.a.AnnotationConfigApplicationContext : Exception thrown from ApplicationListener handling ContextClosedEvent

org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'registrationListener': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1168) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.getTargetBean(ApplicationListenerMethodAdapter.java:371) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:336) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:229) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:166) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:435) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:99) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1120) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1086) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:94) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:82) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.bootstrap.BootstrapApplicationListener$CloseContextOnFailureApplicationListener.onApplicationEvent(BootstrapApplicationListener.java:504) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.context.event.EventPublishingRunListener.failed(EventPublishingRunListener.java:124) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.callFailedListener(SpringApplicationRunListeners.java:96) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.lambda$failed$7(SpringApplicationRunListeners.java:87) ~[spring-boot-2.7.18.jar:2.7.18]
	at java.util.ArrayList.forEach(ArrayList.java:1511) [?:?]
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:120) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.failed(SpringApplicationRunListeners.java:86) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.handleRunFailure(SpringApplication.java:778) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289) [spring-boot-2.7.18.jar:2.7.18]
	at com.CrmApplication.main(CrmApplication.java:27) [classes/:?]

2025-07-30 19:06:07.182  WARN 442152 --- [           main] s.c.a.AnnotationConfigApplicationContext : Exception thrown from ApplicationListener handling ContextClosedEvent

org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'registrationListener': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1168) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.getTargetBean(ApplicationListenerMethodAdapter.java:371) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:336) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:229) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:166) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:435) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:99) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1120) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1086) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:94) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:82) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.bootstrap.BootstrapApplicationListener$CloseContextOnFailureApplicationListener.onApplicationEvent(BootstrapApplicationListener.java:504) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.context.event.EventPublishingRunListener.failed(EventPublishingRunListener.java:124) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.callFailedListener(SpringApplicationRunListeners.java:96) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.lambda$failed$7(SpringApplicationRunListeners.java:87) ~[spring-boot-2.7.18.jar:2.7.18]
	at java.util.ArrayList.forEach(ArrayList.java:1511) [?:?]
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:120) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.failed(SpringApplicationRunListeners.java:86) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.handleRunFailure(SpringApplication.java:778) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289) [spring-boot-2.7.18.jar:2.7.18]
	at com.CrmApplication.main(CrmApplication.java:27) [classes/:?]

2025-07-30 19:06:07.183  WARN 442152 --- [           main] s.c.a.AnnotationConfigApplicationContext : Exception thrown from ApplicationListener handling ContextClosedEvent

org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'registrationListener': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1168) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.getTargetBean(ApplicationListenerMethodAdapter.java:371) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:336) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:229) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:166) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:435) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:99) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1120) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1086) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:94) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:82) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.bootstrap.BootstrapApplicationListener$CloseContextOnFailureApplicationListener.onApplicationEvent(BootstrapApplicationListener.java:504) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.context.event.EventPublishingRunListener.failed(EventPublishingRunListener.java:124) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.callFailedListener(SpringApplicationRunListeners.java:96) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.lambda$failed$7(SpringApplicationRunListeners.java:87) ~[spring-boot-2.7.18.jar:2.7.18]
	at java.util.ArrayList.forEach(ArrayList.java:1511) [?:?]
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:120) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.failed(SpringApplicationRunListeners.java:86) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.handleRunFailure(SpringApplication.java:778) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289) [spring-boot-2.7.18.jar:2.7.18]
	at com.CrmApplication.main(CrmApplication.java:27) [classes/:?]

2025-07-30 19:06:07.183  WARN 442152 --- [           main] s.c.a.AnnotationConfigApplicationContext : Exception thrown from ApplicationListener handling ContextClosedEvent

org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'registrationListener': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1168) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.getTargetBean(ApplicationListenerMethodAdapter.java:371) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:336) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:229) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:166) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:435) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:99) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1120) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1086) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:94) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:82) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.bootstrap.BootstrapApplicationListener$CloseContextOnFailureApplicationListener.onApplicationEvent(BootstrapApplicationListener.java:504) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.context.event.EventPublishingRunListener.failed(EventPublishingRunListener.java:124) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.callFailedListener(SpringApplicationRunListeners.java:96) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.lambda$failed$7(SpringApplicationRunListeners.java:87) ~[spring-boot-2.7.18.jar:2.7.18]
	at java.util.ArrayList.forEach(ArrayList.java:1511) [?:?]
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:120) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.failed(SpringApplicationRunListeners.java:86) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.handleRunFailure(SpringApplication.java:778) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289) [spring-boot-2.7.18.jar:2.7.18]
	at com.CrmApplication.main(CrmApplication.java:27) [classes/:?]

2025-07-30 19:06:07.183  WARN 442152 --- [           main] s.c.a.AnnotationConfigApplicationContext : Exception thrown from ApplicationListener handling ContextClosedEvent

org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'registrationListener': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1168) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.getTargetBean(ApplicationListenerMethodAdapter.java:371) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:336) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:229) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:166) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:435) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:99) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1120) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1086) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:94) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:82) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.bootstrap.BootstrapApplicationListener$CloseContextOnFailureApplicationListener.onApplicationEvent(BootstrapApplicationListener.java:504) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.context.event.EventPublishingRunListener.failed(EventPublishingRunListener.java:124) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.callFailedListener(SpringApplicationRunListeners.java:96) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.lambda$failed$7(SpringApplicationRunListeners.java:87) ~[spring-boot-2.7.18.jar:2.7.18]
	at java.util.ArrayList.forEach(ArrayList.java:1511) [?:?]
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:120) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.failed(SpringApplicationRunListeners.java:86) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.handleRunFailure(SpringApplication.java:778) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289) [spring-boot-2.7.18.jar:2.7.18]
	at com.CrmApplication.main(CrmApplication.java:27) [classes/:?]

2025-07-30 19:06:07.183  WARN 442152 --- [           main] s.c.a.AnnotationConfigApplicationContext : Exception thrown from ApplicationListener handling ContextClosedEvent

org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'registrationListener': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1168) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.getTargetBean(ApplicationListenerMethodAdapter.java:371) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:336) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:229) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:166) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:435) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:99) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1120) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1086) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:94) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:82) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.bootstrap.BootstrapApplicationListener$CloseContextOnFailureApplicationListener.onApplicationEvent(BootstrapApplicationListener.java:504) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.context.event.EventPublishingRunListener.failed(EventPublishingRunListener.java:124) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.callFailedListener(SpringApplicationRunListeners.java:96) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.lambda$failed$7(SpringApplicationRunListeners.java:87) ~[spring-boot-2.7.18.jar:2.7.18]
	at java.util.ArrayList.forEach(ArrayList.java:1511) [?:?]
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:120) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.failed(SpringApplicationRunListeners.java:86) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.handleRunFailure(SpringApplication.java:778) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289) [spring-boot-2.7.18.jar:2.7.18]
	at com.CrmApplication.main(CrmApplication.java:27) [classes/:?]

2025-07-30 19:06:07.189  INFO 442152 --- [           main] c.z.h.HikariDataSource                   : HikariPool-1 - Shutdown initiated...
2025-07-30 19:06:07.193  INFO 442152 --- [           main] c.z.h.HikariDataSource                   : HikariPool-1 - Shutdown completed.
2025-07-30 19:06:07.207 ERROR 442152 --- [           main] o.s.b.SpringApplication                  : Application run failed

java.lang.IllegalStateException: Failed to execute ApplicationRunner
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:759) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746) [spring-boot-2.7.18.jar:2.7.18]
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183) ~[?:?]
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:357) ~[?:?]
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:510) ~[?:?]
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499) ~[?:?]
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150) ~[?:?]
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173) ~[?:?]
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234) ~[?:?]
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596) ~[?:?]
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289) [spring-boot-2.7.18.jar:2.7.18]
	at com.CrmApplication.main(CrmApplication.java:27) [classes/:?]
Caused by: java.lang.reflect.InvocationTargetException
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at com.skyeye.common.base.listener.SkyeyeStartListener.run(SkyeyeStartListener.java:62) ~[classes/:?]
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756) ~[spring-boot-2.7.18.jar:2.7.18]
	... 14 more
Caused by: com.skyeye.exception.CustomException: call rest api failed
	at com.skyeye.common.client.ExecuteFeignClient.get(ExecuteFeignClient.java:34) ~[classes/:?]
	at com.skyeye.common.base.listener.impl.LoadClassEnumListener.run(LoadClassEnumListener.java:76) ~[classes/:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at com.skyeye.common.base.listener.SkyeyeStartListener.run(SkyeyeStartListener.java:62) ~[classes/:?]
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756) ~[spring-boot-2.7.18.jar:2.7.18]
	... 14 more

