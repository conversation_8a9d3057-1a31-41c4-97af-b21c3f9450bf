/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.skyeye.common.base.listener.impl;

import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSON;
import com.skyeye.common.base.classenum.SkyeyeEnumClass;
import com.skyeye.common.base.listener.SkyeyeStartListenerService;
import com.skyeye.common.client.ExecuteFeignClient;
import com.skyeye.common.constans.SkyeyeConstants;
import com.skyeye.common.util.PropertiesUtil;
import com.skyeye.common.util.SpringUtils;
import com.skyeye.eve.classenum.rest.ISkyeyeClassEnumServiceRest;
import feign.Request;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;

/**
 * @ClassName: LoadClassEnumListener
 * @Description: 枚举扫描，扫描所有继承SkyeyeEnumClass的枚举类
 * @author: skyeye云系列--卫志强
 * @date: 2022/9/11 13:30
 * @Copyright: 2022 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
@Component
public class LoadClassEnumListener implements SkyeyeStartListenerService {

    private static Logger LOGGER = LoggerFactory.getLogger(LoadClassEnumListener.class);

    private static final Map<String, String> CONVERT_KEY = new HashMap<>();

    static {
        CONVERT_KEY.put("key", "id");
        CONVERT_KEY.put("value", "name");
    }

    @Override
    public void run() throws Exception {
        LOGGER.info("read skyeye enum start.");
        // 扫描该包路径下所有class文件
        LOGGER.info("read enum basePackage is {}.", SkyeyeConstants.BASE_PACKAGE);
        Set<Class<?>> classes = ClassUtil.scanPackage(SkyeyeConstants.BASE_PACKAGE);
        // 用来存放扫描到的符合要求的【所有枚举】信息
        Map<String, List<Map<String, Object>>> enums = new HashMap<>();
        String applicationName = PropertiesUtil.getPropertiesValue("${spring.application.name}");
        String profilesActive = PropertiesUtil.getPropertiesValue("${spring.profiles.active}");

        for (Class<?> clazz : classes) {
            if (!EnumUtil.isEnum(clazz) || !SkyeyeEnumClass.class.isAssignableFrom(clazz)) {
                continue;
            }
            LOGGER.info("current enum is {}.", clazz.getName());
            // 获取枚举值
            List<Map<String, Object>> enumData = getEnumDataList(clazz, enums, applicationName, profilesActive);
            LOGGER.info("read {} enum, result is {}", clazz.getName(), JSON.toJSONString(enumData));
        }

        // 写入数据库
        Map<String, Object> params = new HashMap<>();
        String appId = PropertiesUtil.getPropertiesValue("${skyeye.appid}");
        params.put("appId", appId);
        params.put("valueList", enums);
        ISkyeyeClassEnumServiceRest iSkyeyeClassEnumServiceRest = SpringUtils.getBean(ISkyeyeClassEnumServiceRest.class);
        // 设置60秒超时
        Request.Options options = new Request.Options(60 * 1000, 60 * 1000);
        ExecuteFeignClient.get(() -> iSkyeyeClassEnumServiceRest.writeClassEnum(params, options));

        LOGGER.info("read skyeye enum end.");
    }

    private static List<Map<String, Object>> getEnumDataList(Class<?> clazz, Map<String, List<Map<String, Object>>> enums, String applicationName, String profilesActive) {
        if (!EnumUtil.isEnum(clazz) || !SkyeyeEnumClass.class.isAssignableFrom(clazz)) {
            return new ArrayList<>();
        }
        String enumKey = applicationName.replace("-" + profilesActive, StringUtils.EMPTY) + "#" + clazz.getName();
        if (enums.containsKey(enumKey)) {
            return enums.get(enumKey);
        }
        // 获取枚举中的所有属性
        List<String> fieldNames = EnumUtil.getFieldNames((Class<? extends Enum<?>>) clazz);
        // 得到 enum 的所有实例：一个数组，该数组包含组成此 Class 对象表示的枚举类的值，按它们声明的顺序
        Object[] objs = clazz.getEnumConstants();
        // 用来存放【当前枚举】的所有属性，装载当前枚举所有值
        List<Map<String, Object>> enumData = new ArrayList<>();
        for (Object obj : objs) {
            Map<String, Object> enumValue = new HashMap<>();
            enumValue.put("enumFiledName", obj.toString());
            for (String field : fieldNames) {
                Object value = getEnumFieldValue(obj, field);
                if (value != null && StringUtils.equals(String.valueOf(value), obj.toString())) {
                    // 读取枚举类时或解析出一个name的属性，该属性和枚举的名字一样，这里加一个判断，如果一样则不要，防止后面出现覆盖的情况
                    continue;
                }
                String key = CONVERT_KEY.containsKey(field) ? CONVERT_KEY.get(field) : field;
                enumValue.put(key, value);
            }
            enumData.add(enumValue);
        }
        // 判断是否有依赖的枚举，并解析组装到一起
        Method dependOnEnum = ReflectUtil.getMethod(clazz, "dependOnEnum");
        if (dependOnEnum != null) {
            List<Class> classList = ReflectUtil.invoke(clazz, dependOnEnum);
            classList.forEach(cla -> enumData.addAll(getEnumDataList(cla, enums, applicationName, profilesActive)));
        }

        enums.put(enumKey, enumData);
        return enumData;
    }

    /**
     * 安全地获取枚举字段值，兼容JDK 17模块系统
     *
     * @param enumObj 枚举对象
     * @param fieldName 字段名
     * @return 字段值，如果获取失败返回null
     */
    private Object getEnumFieldValue(Object enumObj, String fieldName) {
        try {
            // 首先尝试通过getter方法获取值
            String getterName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
            try {
                Method getter = enumObj.getClass().getMethod(getterName);
                return getter.invoke(enumObj);
            } catch (Exception e) {
                // getter方法不存在，尝试直接访问字段
            }

            // 尝试通过字段直接访问
            Field field = enumObj.getClass().getDeclaredField(fieldName);
            // 对于JDK 17，只有当字段是public或者在同一个模块时才能访问
            if (field.canAccess(enumObj) || field.trySetAccessible()) {
                return field.get(enumObj);
            } else {
                LOGGER.warn("Cannot access field '{}' in enum '{}' due to module restrictions",
                    fieldName, enumObj.getClass().getName());
                return null;
            }
        } catch (Exception e) {
            LOGGER.warn("Failed to get field value '{}' from enum '{}': {}",
                fieldName, enumObj.getClass().getName(), e.getMessage());
            return null;
        }
    }

    @Override
    public Integer getOrder() {
        return 3;
    }

}
