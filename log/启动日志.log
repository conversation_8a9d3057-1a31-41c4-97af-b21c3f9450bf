Z:\jdk-17.0.15.6-hotspot\bin\java.exe -XX:TieredStopAtLevel=1 -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true "-Dmanagement.endpoints.jmx.exposure.include=*" "-javaagent:Z:\JetBrains\IntelliJ IDEA 2025.1\lib\idea_rt.jar=60753" -Dfile.encoding=UTF-8 -classpath Z:\IdeaProjects\erp\skyeye-business\skyeye-promote\skyeye-web\target\classes;Z:\IdeaProjects\erp\skyeye-business\skyeye-promote\skyeye-base-server\target\classes;Z:\IdeaProjects\erp\skyeye-business\skyeye-promote\skyeye-entity\target\classes;Z:\IdeaProjects\erp\skyeye-business\skyeye-promote\skyeye-common\target\classes;Z:\IdeaProjects\erp\skyeye-foundation\skyeye-common-rest\target\classes;Z:\IdeaProjects\erp\skyeye-foundation\skyeye-common-module\skyeye-base\target\classes;C:\Users\<USER>\.m2\repository\com\github\binarywang\weixin-java-pay\4.6.0\weixin-java-pay-4.6.0.jar;C:\Users\<USER>\.m2\repository\com\github\binarywang\weixin-java-common\4.6.0\weixin-java-common-4.6.0.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpmime\4.5.14\httpmime-4.5.14.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jcl-over-slf4j\1.7.36\jcl-over-slf4j-1.7.36.jar;C:\Users\<USER>\.m2\repository\com\github\binarywang\qrcode-utils\1.3\qrcode-utils-1.3.jar;C:\Users\<USER>\.m2\repository\org\jodd\jodd-util\6.1.0\jodd-util-6.1.0.jar;C:\Users\<USER>\.m2\repository\com\github\binarywang\wx-java-mp-spring-boot-starter\4.6.0\wx-java-mp-spring-boot-starter-4.6.0.jar;C:\Users\<USER>\.m2\repository\com\github\binarywang\weixin-java-mp\4.6.0\weixin-java-mp-4.6.0.jar;C:\Users\<USER>\.m2\repository\com\github\binarywang\wx-java-miniapp-spring-boot-starter\4.6.0\wx-java-miniapp-spring-boot-starter-4.6.0.jar;C:\Users\<USER>\.m2\repository\com\github\binarywang\weixin-java-miniapp\4.6.0\weixin-java-miniapp-4.6.0.jar;Z:\IdeaProjects\erp\skyeye-business\skyeye-promote\skyeye-api\target\classes;Z:\IdeaProjects\erp\skyeye-business\skyeye-promote\skyeye-organization\target\classes;Z:\IdeaProjects\erp\skyeye-business\skyeye-promote\skyeye-gateway\target\classes;Z:\IdeaProjects\erp\skyeye-business\skyeye-promote\skyeye-quartz\target\classes;Z:\IdeaProjects\erp\skyeye-business\skyeye-promote\skyeye-mq\target\classes;Z:\IdeaProjects\erp\skyeye-business\skyeye-promote\skyeye-userauth\target\classes;Z:\IdeaProjects\erp\skyeye-business\skyeye-promote\skyeye-code-doc\target\classes;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\2.7.18\spring-boot-starter-web-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\2.7.18\spring-boot-starter-json-2.7.18.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.13.5\jackson-datatype-jdk8-2.13.5.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.13.5\jackson-datatype-jsr310-2.13.5.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.13.5\jackson-module-parameter-names-2.13.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\2.7.18\spring-boot-starter-tomcat-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.83\tomcat-embed-core-9.0.83.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\9.0.83\tomcat-embed-el-9.0.83.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.83\tomcat-embed-websocket-9.0.83.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\5.3.31\spring-web-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\5.3.31\spring-beans-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\5.3.31\spring-webmvc-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\5.3.31\spring-context-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\5.3.31\spring-expression-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-actuator\2.7.18\spring-boot-starter-actuator-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator-autoconfigure\2.7.18\spring-boot-actuator-autoconfigure-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator\2.7.18\spring-boot-actuator-2.7.18.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-core\1.9.17\micrometer-core-1.9.17.jar;C:\Users\<USER>\.m2\repository\org\hdrhistogram\HdrHistogram\2.1.12\HdrHistogram-2.1.12.jar;C:\Users\<USER>\.m2\repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;C:\Users\<USER>\.m2\repository\org\jolokia\jolokia-core\1.7.2\jolokia-core-1.7.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\2.7.18\spring-boot-starter-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\2.7.18\spring-boot-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\2.7.18\spring-boot-autoconfigure-2.7.18.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\5.3.31\spring-core-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\5.3.31\spring-jcl-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\1.30\snakeyaml-1.30.jar;C:\Users\<USER>\.m2\repository\org\apache\rocketmq\rocketmq-spring-boot-starter\2.2.1\rocketmq-spring-boot-starter-2.2.1.jar;C:\Users\<USER>\.m2\repository\org\apache\rocketmq\rocketmq-spring-boot\2.2.1\rocketmq-spring-boot-2.2.1.jar;C:\Users\<USER>\.m2\repository\org\apache\rocketmq\rocketmq-client\4.9.1\rocketmq-client-4.9.1.jar;C:\Users\<USER>\.m2\repository\org\apache\rocketmq\rocketmq-common\4.9.1\rocketmq-common-4.9.1.jar;C:\Users\<USER>\.m2\repository\org\apache\rocketmq\rocketmq-acl\4.9.1\rocketmq-acl-4.9.1.jar;C:\Users\<USER>\.m2\repository\org\apache\rocketmq\rocketmq-remoting\4.9.1\rocketmq-remoting-4.9.1.jar;C:\Users\<USER>\.m2\repository\org\apache\rocketmq\rocketmq-logging\4.9.1\rocketmq-logging-4.9.1.jar;C:\Users\<USER>\.m2\repository\org\apache\rocketmq\rocketmq-srvutil\4.9.1\rocketmq-srvutil-4.9.1.jar;C:\Users\<USER>\.m2\repository\commons-cli\commons-cli\1.2\commons-cli-1.2.jar;C:\Users\<USER>\.m2\repository\commons-validator\commons-validator\1.7\commons-validator-1.7.jar;C:\Users\<USER>\.m2\repository\commons-digester\commons-digester\2.1\commons-digester-2.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-validation\2.7.18\spring-boot-starter-validation-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\hibernate\validator\hibernate-validator\6.2.5.Final\hibernate-validator-6.2.5.Final.jar;C:\Users\<USER>\.m2\repository\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar;C:\Users\<USER>\.m2\repository\org\jboss\logging\jboss-logging\3.4.3.Final\jboss-logging-3.4.3.Final.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;C:\Users\<USER>\.m2\repository\com\alibaba\cloud\spring-cloud-starter-alibaba-nacos-config\2021.0.6.1\spring-cloud-starter-alibaba-nacos-config-2021.0.6.1.jar;C:\Users\<USER>\.m2\repository\com\alibaba\cloud\spring-cloud-alibaba-commons\2021.0.6.1\spring-cloud-alibaba-commons-2021.0.6.1.jar;C:\Users\<USER>\.m2\repository\com\alibaba\spring\spring-context-support\1.0.11\spring-context-support-1.0.11.jar;C:\Users\<USER>\.m2\repository\com\alibaba\nacos\nacos-client\2.2.0\nacos-client-2.2.0.jar;C:\Users\<USER>\.m2\repository\com\alibaba\nacos\nacos-auth-plugin\2.2.0\nacos-auth-plugin-2.2.0.jar;C:\Users\<USER>\.m2\repository\com\alibaba\nacos\nacos-encryption-plugin\2.2.0\nacos-encryption-plugin-2.2.0.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpasyncclient\4.1.5\httpasyncclient-4.1.5.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore-nio\4.4.16\httpcore-nio-4.4.16.jar;C:\Users\<USER>\.m2\repository\io\prometheus\simpleclient\0.15.0\simpleclient-0.15.0.jar;C:\Users\<USER>\.m2\repository\io\prometheus\simpleclient_tracer_otel\0.15.0\simpleclient_tracer_otel-0.15.0.jar;C:\Users\<USER>\.m2\repository\io\prometheus\simpleclient_tracer_common\0.15.0\simpleclient_tracer_common-0.15.0.jar;C:\Users\<USER>\.m2\repository\io\prometheus\simpleclient_tracer_otel_agent\0.15.0\simpleclient_tracer_otel_agent-0.15.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-commons\3.1.8\spring-cloud-commons-3.1.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-crypto\5.7.11\spring-security-crypto-5.7.11.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-context\3.1.8\spring-cloud-context-3.1.8.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;C:\Users\<USER>\.m2\repository\com\alibaba\cloud\spring-cloud-starter-alibaba-nacos-discovery\2021.0.6.1\spring-cloud-starter-alibaba-nacos-discovery-2021.0.6.1.jar;C:\Users\<USER>\.m2\repository\com\alibaba\cloud\spring-cloud-starter-alibaba-sentinel\2021.0.6.1\spring-cloud-starter-alibaba-sentinel-2021.0.6.1.jar;C:\Users\<USER>\.m2\repository\com\alibaba\csp\sentinel-transport-simple-http\1.8.6\sentinel-transport-simple-http-1.8.6.jar;C:\Users\<USER>\.m2\repository\com\alibaba\csp\sentinel-transport-common\1.8.6\sentinel-transport-common-1.8.6.jar;C:\Users\<USER>\.m2\repository\com\alibaba\csp\sentinel-datasource-extension\1.8.6\sentinel-datasource-extension-1.8.6.jar;C:\Users\<USER>\.m2\repository\com\alibaba\csp\sentinel-annotation-aspectj\1.8.6\sentinel-annotation-aspectj-1.8.6.jar;C:\Users\<USER>\.m2\repository\com\alibaba\csp\sentinel-core\1.8.6\sentinel-core-1.8.6.jar;C:\Users\<USER>\.m2\repository\com\alibaba\cloud\spring-cloud-circuitbreaker-sentinel\2021.0.6.1\spring-cloud-circuitbreaker-sentinel-2021.0.6.1.jar;C:\Users\<USER>\.m2\repository\com\alibaba\csp\sentinel-reactor-adapter\1.8.6\sentinel-reactor-adapter-1.8.6.jar;C:\Users\<USER>\.m2\repository\com\alibaba\csp\sentinel-spring-webflux-adapter\1.8.6\sentinel-spring-webflux-adapter-1.8.6.jar;C:\Users\<USER>\.m2\repository\com\alibaba\csp\sentinel-spring-webmvc-adapter\1.8.6\sentinel-spring-webmvc-adapter-1.8.6.jar;C:\Users\<USER>\.m2\repository\com\alibaba\csp\sentinel-parameter-flow-control\1.8.6\sentinel-parameter-flow-control-1.8.6.jar;C:\Users\<USER>\.m2\repository\com\googlecode\concurrentlinkedhashmap\concurrentlinkedhashmap-lru\1.4.2\concurrentlinkedhashmap-lru-1.4.2.jar;C:\Users\<USER>\.m2\repository\com\alibaba\csp\sentinel-cluster-server-default\1.8.6\sentinel-cluster-server-default-1.8.6.jar;C:\Users\<USER>\.m2\repository\com\alibaba\csp\sentinel-cluster-common-default\1.8.6\sentinel-cluster-common-default-1.8.6.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.101.Final\netty-handler-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\com\alibaba\csp\sentinel-cluster-client-default\1.8.6\sentinel-cluster-client-default-1.8.6.jar;C:\Users\<USER>\.m2\repository\com\alibaba\cloud\spring-cloud-alibaba-sentinel-datasource\2021.0.6.1\spring-cloud-alibaba-sentinel-datasource-2021.0.6.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter-bootstrap\3.1.8\spring-cloud-starter-bootstrap-3.1.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter\3.1.8\spring-cloud-starter-3.1.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-rsa\1.0.12.RELEASE\spring-security-rsa-1.0.12.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcpkix-jdk18on\1.73\bcpkix-jdk18on-1.73.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcutil-jdk18on\1.73\bcutil-jdk18on-1.73.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter-loadbalancer\3.1.8\spring-cloud-starter-loadbalancer-3.1.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-loadbalancer\3.1.8\spring-cloud-loadbalancer-3.1.8.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.4.34\reactor-core-3.4.34.jar;C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\addons\reactor-extra\3.4.10\reactor-extra-3.4.10.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-cache\2.7.18\spring-boot-starter-cache-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context-support\5.3.31\spring-context-support-5.3.31.jar;C:\Users\<USER>\.m2\repository\com\stoyanr\evictor\1.0.0\evictor-1.0.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter-openfeign\3.1.9\spring-cloud-starter-openfeign-3.1.9.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-openfeign-core\3.1.9\spring-cloud-openfeign-core-3.1.9.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\form\feign-form-spring\3.8.0\feign-form-spring-3.8.0.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\form\feign-form\3.8.0\feign-form-3.8.0.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\feign-core\11.10\feign-core-11.10.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\feign-slf4j\11.10\feign-slf4j-11.10.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\feign-okhttp\11.10\feign-okhttp-11.10.jar;C:\Users\<USER>\.m2\repository\com\squareup\okhttp3\okhttp\4.9.3\okhttp-4.9.3.jar;C:\Users\<USER>\.m2\repository\com\squareup\okio\okio\2.8.0\okio-2.8.0.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-common\1.6.21\kotlin-stdlib-common-1.6.21.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib\1.6.21\kotlin-stdlib-1.6.21.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\annotations\13.0\annotations-13.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-websocket\2.7.18\spring-boot-starter-websocket-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-messaging\5.3.31\spring-messaging-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-websocket\5.3.31\spring-websocket-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-aop\2.7.18\spring-boot-starter-aop-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\5.3.31\spring-aop-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.7\aspectjweaver-1.9.7.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-log4j2\2.7.18\spring-boot-starter-log4j2-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-slf4j-impl\2.17.2\log4j-slf4j-impl-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-jul\2.17.2\log4j-jul-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\1.7.36\jul-to-slf4j-1.7.36.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.17.0\log4j-api-2.17.0.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-core\2.17.0\log4j-core-2.17.0.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-boot-starter\3.5.1\mybatis-plus-boot-starter-3.5.1.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus\3.5.1\mybatis-plus-3.5.1.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-extension\3.5.1\mybatis-plus-extension-3.5.1.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-core\3.5.1\mybatis-plus-core-3.5.1.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-annotation\3.5.1\mybatis-plus-annotation-3.5.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\2.7.18\spring-boot-starter-jdbc-2.7.18.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\5.3.31\spring-jdbc-5.3.31.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-generator\3.5.1\mybatis-plus-generator-3.5.1.jar;C:\Users\<USER>\.m2\repository\com\github\yulichang\mybatis-plus-join-boot-starter\1.4.13\mybatis-plus-join-boot-starter-1.4.13.jar;C:\Users\<USER>\.m2\repository\com\github\yulichang\mybatis-plus-join-extension\1.4.13\mybatis-plus-join-extension-1.4.13.jar;C:\Users\<USER>\.m2\repository\com\github\yulichang\mybatis-plus-join-core\1.4.13\mybatis-plus-join-core-1.4.13.jar;C:\Users\<USER>\.m2\repository\com\github\yulichang\mybatis-plus-join-annotation\1.4.13\mybatis-plus-join-annotation-1.4.13.jar;C:\Users\<USER>\.m2\repository\com\github\yulichang\mybatis-plus-join-adapter-v33x\1.4.13\mybatis-plus-join-adapter-v33x-1.4.13.jar;C:\Users\<USER>\.m2\repository\com\github\yulichang\mybatis-plus-join-adapter-base\1.4.13\mybatis-plus-join-adapter-base-1.4.13.jar;C:\Users\<USER>\.m2\repository\com\github\yulichang\mybatis-plus-join-adapter-jsqlparser\1.4.13\mybatis-plus-join-adapter-jsqlparser-1.4.13.jar;C:\Users\<USER>\.m2\repository\com\github\yulichang\mybatis-plus-join-adapter-jsqlparser-v46\1.4.13\mybatis-plus-join-adapter-jsqlparser-v46-1.4.13.jar;C:\Users\<USER>\.m2\repository\com\github\yulichang\mybatis-plus-join-adapter-v3431\1.4.13\mybatis-plus-join-adapter-v3431-1.4.13.jar;C:\Users\<USER>\.m2\repository\com\github\yulichang\mybatis-plus-join-adapter-v352\1.4.13\mybatis-plus-join-adapter-v352-1.4.13.jar;C:\Users\<USER>\.m2\repository\com\github\yulichang\mybatis-plus-join-adapter-v355\1.4.13\mybatis-plus-join-adapter-v355-1.4.13.jar;C:\Users\<USER>\.m2\repository\mysql\mysql-connector-java\8.0.16\mysql-connector-java-8.0.16.jar;C:\Users\<USER>\.m2\repository\com\alibaba\druid-spring-boot-starter\1.2.18\druid-spring-boot-starter-1.2.18.jar;C:\Users\<USER>\.m2\repository\com\alibaba\druid\1.2.18\druid-1.2.18.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-runtime\2.3.9\jaxb-runtime-2.3.9.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\txw2\2.3.9\txw2-2.3.9.jar;C:\Users\<USER>\.m2\repository\com\sun\istack\istack-commons-runtime\3.0.12\istack-commons-runtime-3.0.12.jar;C:\Users\<USER>\.m2\repository\com\sun\activation\jakarta.activation\1.2.2\jakarta.activation-1.2.2.jar;C:\Users\<USER>\.m2\repository\com\github\pagehelper\pagehelper-spring-boot-starter\1.4.2\pagehelper-spring-boot-starter-1.4.2.jar;C:\Users\<USER>\.m2\repository\org\mybatis\spring\boot\mybatis-spring-boot-starter\2.2.2\mybatis-spring-boot-starter-2.2.2.jar;C:\Users\<USER>\.m2\repository\org\mybatis\spring\boot\mybatis-spring-boot-autoconfigure\2.2.2\mybatis-spring-boot-autoconfigure-2.2.2.jar;C:\Users\<USER>\.m2\repository\org\mybatis\mybatis\3.5.9\mybatis-3.5.9.jar;C:\Users\<USER>\.m2\repository\org\mybatis\mybatis-spring\2.0.7\mybatis-spring-2.0.7.jar;C:\Users\<USER>\.m2\repository\com\github\pagehelper\pagehelper-spring-boot-autoconfigure\1.4.2\pagehelper-spring-boot-autoconfigure-1.4.2.jar;C:\Users\<USER>\.m2\repository\com\github\pagehelper\pagehelper\5.3.0\pagehelper-5.3.0.jar;C:\Users\<USER>\.m2\repository\com\github\jsqlparser\jsqlparser\4.2\jsqlparser-4.2.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;C:\Users\<USER>\.m2\repository\org\jsoup\jsoup\1.18.1\jsoup-1.18.1.jar;C:\Users\<USER>\.m2\repository\net\sf\json-lib\json-lib\2.4\json-lib-2.4-jdk15.jar;C:\Users\<USER>\.m2\repository\commons-beanutils\commons-beanutils\1.8.0\commons-beanutils-1.8.0.jar;C:\Users\<USER>\.m2\repository\commons-collections\commons-collections\3.2.1\commons-collections-3.2.1.jar;C:\Users\<USER>\.m2\repository\commons-lang\commons-lang\2.5\commons-lang-2.5.jar;C:\Users\<USER>\.m2\repository\commons-logging\commons-logging\1.1.1\commons-logging-1.1.1.jar;C:\Users\<USER>\.m2\repository\net\sf\ezmorph\ezmorph\1.0.6\ezmorph-1.0.6.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpclient\4.5.14\httpclient-4.5.14.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;C:\Users\<USER>\.m2\repository\org\quartz-scheduler\quartz\2.3.2\quartz-2.3.2.jar;C:\Users\<USER>\.m2\repository\com\mchange\mchange-commons-java\0.2.15\mchange-commons-java-0.2.15.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.30\lombok-1.18.30.jar;C:\Users\<USER>\.m2\repository\javax\persistence\persistence-api\1.0.2\persistence-api-1.0.2.jar;C:\Users\<USER>\.m2\repository\io\minio\minio\8.5.7\minio-8.5.7.jar;C:\Users\<USER>\.m2\repository\com\carrotsearch\thirdparty\simple-xml-safe\2.7.1\simple-xml-safe-2.7.1.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.13.5\jackson-annotations-2.13.5.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.13.5\jackson-core-2.13.5.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.13.5\jackson-databind-2.13.5.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcprov-jdk18on\1.76\bcprov-jdk18on-1.76.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-compress\1.24.0\commons-compress-1.24.0.jar;C:\Users\<USER>\.m2\repository\org\xerial\snappy\snappy-java\********\snappy-java-********.jar;C:\Users\<USER>\.m2\repository\com\alipay\sdk\alipay-sdk-java\4.35.79.ALL\alipay-sdk-java-4.35.79.ALL.jar;C:\Users\<USER>\.m2\repository\com\alibaba\fastjson\1.2.83_noneautotype\fastjson-1.2.83_noneautotype.jar;C:\Users\<USER>\.m2\repository\dom4j\dom4j\1.6.1\dom4j-1.6.1.jar;C:\Users\<USER>\.m2\repository\xml-apis\xml-apis\1.0.b2\xml-apis-1.0.b2.jar;C:\Users\<USER>\.m2\repository\com\xingyuv\spring-boot-starter-justauth\1.0.8\spring-boot-starter-justauth-1.0.8.jar;C:\Users\<USER>\.m2\repository\com\xingyuv\justauth\1.0.8\justauth-1.0.8.jar;C:\Users\<USER>\.m2\repository\com\xingyuv\simple-http\1.0.8\simple-http-1.0.8.jar;C:\Users\<USER>\.m2\repository\joda-time\joda-time\2.10.10\joda-time-2.10.10.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-devtools\2.7.18\spring-boot-devtools-2.7.18.jar;C:\Users\<USER>\.m2\repository\commons-fileupload\commons-fileupload\1.3.1\commons-fileupload-1.3.1.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.2\commons-io-2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-redis\2.7.18\spring-boot-starter-data-redis-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-redis\2.7.18\spring-data-redis-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-keyvalue\2.7.18\spring-data-keyvalue-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\2.7.18\spring-data-commons-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\5.3.31\spring-tx-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-oxm\5.3.31\spring-oxm-5.3.31.jar;C:\Users\<USER>\.m2\repository\io\lettuce\lettuce-core\6.1.10.RELEASE\lettuce-core-6.1.10.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.101.Final\netty-common-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.101.Final\netty-transport-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\redis\clients\jedis\3.8.0\jedis-3.8.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-pool2\2.11.1\commons-pool2-2.11.1.jar;C:\Users\<USER>\.m2\repository\org\freemarker\freemarker\2.3.32\freemarker-2.3.32.jar;C:\Users\<USER>\.m2\repository\com\sun\mail\javax.mail\1.6.2\javax.mail-1.6.2.jar;C:\Users\<USER>\.m2\repository\javax\activation\activation\1.1\activation-1.1.jar;C:\Users\<USER>\.m2\repository\com\itextpdf\itextpdf\5.5.6\itextpdf-5.5.6.jar;C:\Users\<USER>\.m2\repository\com\itextpdf\itext-asian\5.2.0\itext-asian-5.2.0.jar;C:\Users\<USER>\.m2\repository\com\itextpdf\html2pdf\4.0.5\html2pdf-4.0.5.jar;C:\Users\<USER>\.m2\repository\com\itextpdf\forms\7.2.5\forms-7.2.5.jar;C:\Users\<USER>\.m2\repository\com\itextpdf\svg\7.2.5\svg-7.2.5.jar;C:\Users\<USER>\.m2\repository\com\itextpdf\styled-xml-parser\7.2.5\styled-xml-parser-7.2.5.jar;C:\Users\<USER>\.m2\repository\com\itextpdf\layout\7.2.5\layout-7.2.5.jar;C:\Users\<USER>\.m2\repository\com\itextpdf\io\7.2.5\io-7.2.5.jar;C:\Users\<USER>\.m2\repository\com\itextpdf\commons\7.2.5\commons-7.2.5.jar;C:\Users\<USER>\.m2\repository\com\itextpdf\kernel\7.2.5\kernel-7.2.5.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcpkix-jdk15on\1.70\bcpkix-jdk15on-1.70.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcutil-jdk15on\1.70\bcutil-jdk15on-1.70.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcprov-jdk15on\1.70\bcprov-jdk15on-1.70.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\28.0-jre\guava-28.0-jre.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\2.8.1\checker-qual-2.8.1.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.3.2\error_prone_annotations-2.3.2.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\1.3\j2objc-annotations-1.3.jar;C:\Users\<USER>\.m2\repository\org\codehaus\mojo\animal-sniffer-annotations\1.17\animal-sniffer-annotations-1.17.jar;C:\Users\<USER>\.m2\repository\cn\hutool\hutool-all\5.8.39\hutool-all-5.8.39.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi\4.1.2\poi-4.1.2.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-collections4\4.4\commons-collections4-4.4.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-math3\3.6.1\commons-math3-3.6.1.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\SparseBitSet\1.2\SparseBitSet-1.2.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml-schemas\4.1.2\poi-ooxml-schemas-4.1.2.jar;C:\Users\<USER>\.m2\repository\org\apache\xmlbeans\xmlbeans\3.1.0\xmlbeans-3.1.0.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-ooxml\4.1.2\poi-ooxml-4.1.2.jar;C:\Users\<USER>\.m2\repository\com\github\virtuald\curvesapi\1.06\curvesapi-1.06.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\poi-scratchpad\4.1.2\poi-scratchpad-4.1.2.jar;C:\Users\<USER>\.m2\repository\fr\opensagres\xdocreport\fr.opensagres.poi.xwpf.converter.xhtml\2.0.2\fr.opensagres.poi.xwpf.converter.xhtml-2.0.2.jar;C:\Users\<USER>\.m2\repository\fr\opensagres\xdocreport\fr.opensagres.poi.xwpf.converter.core\2.0.2\fr.opensagres.poi.xwpf.converter.core-2.0.2.jar;C:\Users\<USER>\.m2\repository\org\apache\poi\ooxml-schemas\1.4\ooxml-schemas-1.4.jar;C:\Users\<USER>\.m2\repository\fr\opensagres\xdocreport\fr.opensagres.xdocreport.core\2.0.2\fr.opensagres.xdocreport.core-2.0.2.jar;C:\Users\<USER>\.m2\repository\com\auth0\java-jwt\3.10.3\java-jwt-3.10.3.jar;C:\Users\<USER>\.m2\repository\com\google\zxing\core\3.3.3\core-3.3.3.jar;C:\Users\<USER>\.m2\repository\com\google\zxing\javase\3.3.3\javase-3.3.3.jar;C:\Users\<USER>\.m2\repository\com\beust\jcommander\1.72\jcommander-1.72.jar;C:\Users\<USER>\.m2\repository\com\github\jai-imageio\jai-imageio-core\1.4.0\jai-imageio-core-1.4.0.jar;C:\Users\<USER>\.m2\repository\com\belerweb\pinyin4j\2.5.0\pinyin4j-2.5.0.jar;C:\Users\<USER>\.m2\repository\com\google\protobuf\protobuf-java\3.4.0\protobuf-java-3.4.0.jar;C:\Users\<USER>\.m2\repository\cn\afterturn\easypoi-base\4.2.0\easypoi-base-4.2.0.jar;C:\Users\<USER>\.m2\repository\ognl\ognl\3.2.6\ognl-3.2.6.jar;C:\Users\<USER>\.m2\repository\org\javassist\javassist\3.20.0-GA\javassist-3.20.0-GA.jar;C:\Users\<USER>\.m2\repository\javax\validation\validation-api\2.0.1.Final\validation-api-2.0.1.Final.jar;C:\Users\<USER>\.m2\repository\cn\afterturn\easypoi-annotation\4.2.0\easypoi-annotation-4.2.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-jexl\2.1.1\commons-jexl-2.1.1.jar;C:\Users\<USER>\.m2\repository\org\dom4j\dom4j\2.1.1\dom4j-2.1.1.jar;C:\Users\<USER>\.m2\repository\org\openoffice\juh\4.1.2\juh-4.1.2.jar;C:\Users\<USER>\.m2\repository\org\openoffice\jurt\4.1.2\jurt-4.1.2.jar;C:\Users\<USER>\.m2\repository\org\openoffice\ridl\4.1.2\ridl-4.1.2.jar;C:\Users\<USER>\.m2\repository\org\openoffice\unoil\4.1.2\unoil-4.1.2.jar;C:\Users\<USER>\.m2\repository\com\thoughtworks\xstream\xstream\1.4.10\xstream-1.4.10.jar;C:\Users\<USER>\.m2\repository\xmlpull\xmlpull\1.1.3.1\xmlpull-1.1.3.1.jar;C:\Users\<USER>\.m2\repository\xpp3\xpp3_min\1.1.4c\xpp3_min-1.1.4c.jar;C:\Users\<USER>\.m2\repository\com\artofsolving\jodconverter\2.2.1\jodconverter-2.2.1.jar;Z:\IdeaProjects\erp\skyeye-infrastructure\xxl-job-2.3.0\xxl-job-core\target\classes;C:\Users\<USER>\.m2\repository\io\netty\netty-all\4.1.101.Final\netty-all-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.101.Final\netty-buffer-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.101.Final\netty-codec-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-dns\4.1.101.Final\netty-codec-dns-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-haproxy\4.1.101.Final\netty-codec-haproxy-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http\4.1.101.Final\netty-codec-http-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http2\4.1.101.Final\netty-codec-http2-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-memcache\4.1.101.Final\netty-codec-memcache-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-mqtt\4.1.101.Final\netty-codec-mqtt-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-redis\4.1.101.Final\netty-codec-redis-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-smtp\4.1.101.Final\netty-codec-smtp-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-socks\4.1.101.Final\netty-codec-socks-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-stomp\4.1.101.Final\netty-codec-stomp-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-xml\4.1.101.Final\netty-codec-xml-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-unix-common\4.1.101.Final\netty-transport-native-unix-common-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler-proxy\4.1.101.Final\netty-handler-proxy-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler-ssl-ocsp\4.1.101.Final\netty-handler-ssl-ocsp-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.101.Final\netty-resolver-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns\4.1.101.Final\netty-resolver-dns-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-rxtx\4.1.101.Final\netty-transport-rxtx-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-sctp\4.1.101.Final\netty-transport-sctp-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-udt\4.1.101.Final\netty-transport-udt-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-classes-epoll\4.1.101.Final\netty-transport-classes-epoll-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-classes-kqueue\4.1.101.Final\netty-transport-classes-kqueue-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-classes-macos\4.1.101.Final\netty-resolver-dns-classes-macos-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-epoll\4.1.101.Final\netty-transport-native-epoll-4.1.101.Final-linux-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-epoll\4.1.101.Final\netty-transport-native-epoll-4.1.101.Final-linux-aarch_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-kqueue\4.1.101.Final\netty-transport-native-kqueue-4.1.101.Final-osx-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-kqueue\4.1.101.Final\netty-transport-native-kqueue-4.1.101.Final-osx-aarch_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-native-macos\4.1.101.Final\netty-resolver-dns-native-macos-4.1.101.Final-osx-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-native-macos\4.1.101.Final\netty-resolver-dns-native-macos-4.1.101.Final-osx-aarch_64.jar;C:\Users\<USER>\.m2\repository\com\google\code\gson\gson\2.9.1\gson-2.9.1.jar;C:\Users\<USER>\.m2\repository\org\codehaus\groovy\groovy\3.0.19\groovy-3.0.19.jar;C:\Users\<USER>\.m2\repository\com\googlecode\json-simple\json-simple\1.1.1\json-simple-1.1.1.jar;C:\Users\<USER>\.m2\repository\de\codecentric\spring-boot-admin-starter-client\2.7.15\spring-boot-admin-starter-client-2.7.15.jar;C:\Users\<USER>\.m2\repository\de\codecentric\spring-boot-admin-client\2.7.15\spring-boot-admin-client-2.7.15.jar com.SkyEyeApplication
2025-07-30 22:40:47.548  INFO 448872 --- [           main] c.a.n.c.e.SearchableProperties           : properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
/**
 *                             _ooOoo_
 *                            o8888888o
 *                            88" . "88
 *                            (| -_- |)
 *                            O\  =  /O
 *                         ____/`---'\____
 *                       .'  \\|     |//  `.
 *                      /  \\|||  :  |||//  \
 *                     /  _||||| -:- |||||-  \
 *                     |   | \\\  -  /// |   |
 *                     | \_|  ''\---/''  |   |
 *                     \  .-\__  `-`  ___/-. /
 *                   ___`. .'  /--.--\  `. . __
 *                ."" '<  `.___\_<|>_/___.'  >'"".
 *               | | :  `- \`.;`\ _ /`;.`/ - ` : | |
 *               \  \ `-.   \_ __\ /__ _/   .-` /  /
 *          ======`-.____`-.___\_____/___.-`____.-'======
 *                             `=---='
 *          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
 *                     佛祖保佑        永无BUG
 *            佛曰:
 *                   写字楼里写字间，写字间里程序员；
 *                   程序人员写程序，又拿程序换酒钱。
 *                   酒醒只在网上坐，酒醉还来网下眠；
 *                   酒醉酒醒日复日，网上网下年复年。
 *                   但愿老死电脑间，不愿鞠躬老板前；
 *                   奔驰宝马贵者趣，公交自行程序员。
 *                   别人笑我忒疯癫，我笑自己命太贱；
 *                   不见满街漂亮妹，哪个归得程序员？
 *		:: springboot 2.x
 *      :: Skyeye系列框架，作者：卫志强
 *      :: 作者独家专属，未购买禁止私自使用
 *      启动：nohup java -Xms800m -Xmx800m -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=512m -jar -Dspring.cloud.nacos.discovery.server-addr=localhost:9000 -Dspring.cloud.nacos.config.server-addr=localhost:9000  -Dspring.profiles.active=dev skyeye-web.jar >> /opt/service/project/nohup-pro.out 2>&1 &
 *      清空日志：cat /dev/null > xxx-web.out
*/
2025-07-30 22:40:49.247  INFO 448872 --- [           main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-30 22:40:49.247  INFO 448872 --- [           main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-30 22:40:50.098  WARN 448872 --- [           main] c.a.c.n.c.NacosPropertySourceBuilder     : Ignore the empty nacos configuration and get it based on dataId[skyeye-pro-dev] & group[DEFAULT_GROUP]
2025-07-30 22:40:50.191  WARN 448872 --- [           main] c.a.c.n.c.NacosPropertySourceBuilder     : Ignore the empty nacos configuration and get it based on dataId[skyeye-pro-dev-dev.yml] & group[DEFAULT_GROUP]
2025-07-30 22:40:50.192  INFO 448872 --- [           main] b.c.PropertySourceBootstrapConfiguration : Located property source: [BootstrapPropertySource {name='bootstrapProperties-skyeye-pro-dev-dev.yml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-skyeye-pro-dev.yml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-skyeye-pro-dev,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-skyeye-common.yml,DEFAULT_GROUP'}]
2025-07-30 22:40:50.226  INFO 448872 --- [           main] c.SkyEyeApplication                      : The following 1 profile is active: "dev"
2025-07-30 22:40:55.460  INFO 448872 --- [           main] o.s.c.c.s.GenericScope                   : BeanFactory id=0da65742-9a55-3769-9795-078fa5663fba
2025-07-30 22:40:56.051  INFO 448872 --- [           main] o.s.b.w.e.t.TomcatWebServer              : Tomcat initialized with port(s): 8081 (http)
2025-07-30 22:40:56.063  INFO 448872 --- [           main] o.a.c.c.StandardService                  : Starting service [Tomcat]
2025-07-30 22:40:56.063  INFO 448872 --- [           main] o.a.c.c.StandardEngine                   : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-30 22:40:56.226  INFO 448872 --- [           main] o.a.c.c.C.[.[.[/]                        : Initializing Spring embedded WebApplicationContext
2025-07-30 22:40:56.226  INFO 448872 --- [           main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 5989 ms
Loading class `com.mysql.jdbc.Driver'. This is deprecated. The new driver class is `com.mysql.cj.jdbc.Driver'. The driver is automatically registered via the SPI and manual loading of the driver class is generally unnecessary.
2025-07-30 22:40:56.419  INFO 448872 --- [           main] o.s.b.a.e.w.ServletEndpointRegistrar     : Registered '/actuator/jolokia' to jolokia-actuator-endpoint
2025-07-30 22:40:56.618  INFO 448872 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 22:40:56.666  INFO 448872 --- [           main] c.z.h.HikariDataSource                   : HikariPool-1 - Starting...
2025-07-30 22:40:56.679  WARN 448872 --- [           main] c.z.h.u.DriverDataSource                 : Registered driver with driverClassName=com.mysql.jdbc.Driver was not found, trying direct instantiation.
2025-07-30 22:40:56.810  INFO 448872 --- [           main] c.z.h.HikariDataSource                   : HikariPool-1 - Start completed.
2025-07-30 22:40:56.814  INFO 448872 --- [           main] c.s.d.c.BaseDataSourceConfig             : database product name: 'MySQL'
2025-07-30 22:40:56.814  INFO 448872 --- [           main] c.s.d.c.BaseDataSourceConfig             : using database type: mysql
 _ _   |_  _ _|_. ___ _ |    _
| | |\/|_)(_| | |_\  |_)||_|_\
     /               |
                        3.5.1
2025-07-30 22:40:57.823  INFO 448872 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 22:40:57.840  INFO 448872 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 22:40:57.852  INFO 448872 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 22:40:57.858  INFO 448872 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 22:40:57.864  INFO 448872 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 22:40:59.130  INFO 448872 --- [           main] o.s.c.c.u.InetUtils                      : Cannot determine local hostname
2025-07-30 22:41:11.239  INFO 448872 --- [           main] o.s.c.c.u.InetUtils                      : Cannot determine local hostname
2025-07-30 22:41:12.276  INFO 448872 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-report-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 22:41:12.532  INFO 448872 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 22:41:14.098  INFO 448872 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 22:41:15.303  INFO 448872 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-crm-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 22:41:15.311  INFO 448872 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-crm-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 22:41:15.328  INFO 448872 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-erp-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 22:41:15.335  INFO 448872 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-erp-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 22:41:15.341  INFO 448872 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-erp-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 22:41:15.347  INFO 448872 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-erp-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 22:41:15.353  INFO 448872 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 22:41:15.358  INFO 448872 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 22:41:15.365  INFO 448872 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 22:41:15.372  INFO 448872 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 22:41:15.378  INFO 448872 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 22:41:15.393  INFO 448872 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-adm-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 22:41:15.399  INFO 448872 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-adm-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 22:41:15.414  INFO 448872 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-ifs-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 22:41:15.422  INFO 448872 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 22:41:15.428  INFO 448872 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 22:41:15.440  INFO 448872 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 22:41:15.446  INFO 448872 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 22:41:15.469  INFO 448872 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-shop-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 22:41:15.484  INFO 448872 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-wages-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 22:41:15.493  INFO 448872 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 22:41:15.505  INFO 448872 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 22:41:15.514  WARN 448872 --- [           main] c.s.c.u.IPSeeker                         : load file path config error.
null/util/qqwry.dat
IP地址信息文件没有找到，IP显示功能将无法使用
2025-07-30 22:41:15.514  WARN 448872 --- [           main] c.s.c.u.IPSeeker                         : load file path config error.
null/util/qqwry.dat
IP地址信息文件没有找到，IP显示功能将无法使用
2025-07-30 22:41:15.527  INFO 448872 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 22:41:15.545  INFO 448872 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-flowable-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 22:41:15.551  INFO 448872 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 22:41:16.126  INFO 448872 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'xxl-job-dev' URL not provided. Will try picking an instance via load-balancing.
RocketMQLog:WARN No appenders could be found for logger (io.netty.util.concurrent.GlobalEventExecutor).
RocketMQLog:WARN Please initialize the logger system properly.
2025-07-30 22:41:19.274  INFO 448872 --- [           main] c.s.c.n.NacosConfigService               : ==========创建configService实例===============
2025-07-30 22:41:19.275  INFO 448872 --- [           main] c.s.c.x.XxlJobConfig                     : >>>>>>>>>>> xxl-job config init.
2025-07-30 22:41:19.744  INFO 448872 --- [           main] c.a.c.s.SentinelWebMvcConfigurer         : [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-30 22:41:20.116  INFO 448872 --- [           main] c.g.y.a.MybatisPlusJoinAutoConfiguration : mybatis plus join properties config complete
2025-07-30 22:41:20.364  INFO 448872 --- [lientSelector_1] RocketmqRemoting                         : closeChannel: close the connection to remote address[**************:10911] result: true
 _ _   |_  _ _|_. ___ _ |    _  .  _  .  _
| | |\/|_)(_| | |_\  |_)||_|_\  | (_) | | |
     /               |          /
                                    1.4.13
2025-07-30 22:41:20.452  INFO 448872 --- [           main] c.g.y.a.MybatisPlusJoinAutoConfiguration : mybatis plus join SqlInjector init
2025-07-30 22:41:20.594  INFO 448872 --- [           main] o.q.i.StdSchedulerFactory                : Using default implementation for ThreadExecutor
2025-07-30 22:41:20.603  INFO 448872 --- [           main] o.q.c.SchedulerSignalerImpl              : Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-30 22:41:20.603  INFO 448872 --- [           main] o.q.c.QuartzScheduler                    : Quartz Scheduler v.2.3.2 created.
2025-07-30 22:41:20.603  INFO 448872 --- [           main] o.q.s.RAMJobStore                        : RAMJobStore initialized.
2025-07-30 22:41:20.604  INFO 448872 --- [           main] o.q.c.QuartzScheduler                    : Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-30 22:41:20.604  INFO 448872 --- [           main] o.q.i.StdSchedulerFactory                : Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-30 22:41:20.604  INFO 448872 --- [           main] o.q.i.StdSchedulerFactory                : Quartz scheduler version: 2.3.2
2025-07-30 22:41:20.604  INFO 448872 --- [           main] o.q.c.QuartzScheduler                    : JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@5ac6d58b
2025-07-30 22:41:21.149  WARN 448872 --- [           main] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-30 22:41:21.156  INFO 448872 --- [           main] o.s.b.a.e.w.EndpointLinksResolver        : Exposing 21 endpoint(s) beneath base path '/actuator'
2025-07-30 22:41:21.347  INFO 448872 --- [           main] c.x.j.c.e.XxlJobExecutor                 : >>>>>>>>>>> xxl-job register jobhandler success, name:annualLeaveStatisticsQuartz, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@426fc6df[class com.skyeye.sys.quartz.AnnualLeaveStatisticsQuartz#annualLeaveStatistics]
2025-07-30 22:41:21.437  INFO 448872 --- [           main] c.x.j.c.e.XxlJobExecutor                 : >>>>>>>>>>> xxl-job register jobhandler success, name:deleteOfficeDocumentOnlineUserService, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@68b3a99b[class com.skyeye.xxljob.OfficeXxlJob#cleanInactiveUsers]
2025-07-30 22:41:21.517  INFO 448872 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 22:41:21.520  INFO 448872 --- [           main] o.s.c.o.FeignClientFactoryBean           : For 'skyeye-pro-dev' URL not provided. Will try picking an instance via load-balancing.
2025-07-30 22:41:21.577  WARN 448872 --- [           main] c.x.j.c.e.XxlJobExecutor                 : >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-07-30 22:41:21.674  INFO 448872 --- [      Thread-31] c.x.j.c.s.EmbedServer                    : >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 18081
2025-07-30 22:41:21.694  INFO 448872 --- [           main] a.r.s.s.DefaultRocketMQListenerContainer : running container: DefaultRocketMQListenerContainer{consumerGroup='NOTICE_SEND_SERVICE', nameServer='*************:9876', topic='NOTICE_SEND_SERVICE', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='dev', messageModel=CLUSTERING}
2025-07-30 22:41:21.694  INFO 448872 --- [           main] o.a.r.s.a.ListenerContainerConfiguration : Register the listener to container, listenerBeanName:noticeSendServiceImpl, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-30 22:41:21.744  INFO 448872 --- [           main] a.r.s.s.DefaultRocketMQListenerContainer : running container: DefaultRocketMQListenerContainer{consumerGroup='ORDINARY_MAIL_DELIVERY_SERVICE', nameServer='*************:9876', topic='ORDINARY_MAIL_DELIVERY_SERVICE', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='dev', messageModel=CLUSTERING}
2025-07-30 22:41:21.744  INFO 448872 --- [           main] o.a.r.s.a.ListenerContainerConfiguration : Register the listener to container, listenerBeanName:ordinaryMailDeliveryServiceImpl, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-30 22:41:21.786  INFO 448872 --- [           main] a.r.s.s.DefaultRocketMQListenerContainer : running container: DefaultRocketMQListenerContainer{consumerGroup='SMS_SEND_SERVICE', nameServer='*************:9876', topic='SMS_SEND_SERVICE', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='dev', messageModel=CLUSTERING}
2025-07-30 22:41:21.786  INFO 448872 --- [           main] o.a.r.s.a.ListenerContainerConfiguration : Register the listener to container, listenerBeanName:smsSendConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-30 22:41:21.816  INFO 448872 --- [           main] o.s.j.e.a.AnnotationMBeanExporter        : Bean with key 'executorConfig' has been registered as an MBean but has no exposed attributes or operations
2025-07-30 22:41:21.852  INFO 448872 --- [           main] o.s.b.w.e.t.TomcatWebServer              : Tomcat started on port(s): 8081 (http) with context path ''
2025-07-30 22:41:23.018  INFO 448872 --- [           main] o.s.c.c.u.InetUtils                      : Cannot determine local hostname
2025-07-30 22:41:23.204  INFO 448872 --- [           main] c.a.c.n.r.NacosServiceRegistry           : nacos registry, DEFAULT_GROUP skyeye-pro-dev *************:8081 register finished
2025-07-30 22:41:23.205  INFO 448872 --- [           main] o.s.s.q.SchedulerFactoryBean             : Starting Quartz Scheduler now
2025-07-30 22:41:23.205  INFO 448872 --- [           main] o.q.c.QuartzScheduler                    : Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-30 22:41:23.218  INFO 448872 --- [           main] c.SkyEyeApplication                      : Started SkyEyeApplication in 37.169 seconds (JVM running for 38.022)
2025-07-30 22:41:23.221  INFO 448872 --- [           main] c.s.c.b.l.SkyeyeStartListener            : ================================================================================
2025-07-30 22:41:23.221  INFO 448872 --- [           main] c.s.c.b.l.SkyeyeStartListener            : 开始加载 Skyeye 智能制造云办公管理系统 启动类
2025-07-30 22:41:23.222  INFO 448872 --- [           main] c.s.c.b.l.SkyeyeStartListener            : loadApiFromXmlListener start.
2025-07-30 22:41:23.222  INFO 448872 --- [           main] c.s.c.b.l.i.LoadApiFromXmlListener       : start loading xml [线程读取配置信息路径开始]
2025-07-30 22:41:23.224  INFO 448872 --- [           main] c.s.c.b.l.i.LoadApiFromXmlListener       : loading resource name is [gateway.xml]
2025-07-30 22:41:23.225  INFO 448872 --- [           main] c.s.c.b.l.i.LoadApiFromXmlListener       : loading resource name is [userauth.xml]
2025-07-30 22:41:23.226  INFO 448872 --- [           main] c.s.c.b.l.i.LoadApiFromXmlListener       : loading resource name is [codedoc.xml]
2025-07-30 22:41:23.226  INFO 448872 --- [           main] c.s.c.b.l.i.LoadApiFromXmlListener       : end loading xml [线程读取配置信息路径结束]
2025-07-30 22:41:23.226  INFO 448872 --- [           main] c.s.c.b.l.i.LoadApiFromXmlListener       : Read request path file completed: [36] interfaces in total
2025-07-30 22:41:23.226  INFO 448872 --- [           main] c.s.c.b.l.SkyeyeStartListener            : loadApiFromXmlListener executor end.
2025-07-30 22:41:23.227  INFO 448872 --- [           main] c.s.c.b.l.SkyeyeStartListener            : loadApiFromAnnoListener start.
2025-07-30 22:41:23.261  INFO 448872 --- [           main] c.s.c.b.l.i.LoadApiFromAnnoListener      : ApiListener start.
2025-07-30 22:41:23.527  INFO 448872 --- [           main] c.s.c.b.l.i.LoadApiFromAnnoListener      : anno load Rest API num is 390.
2025-07-30 22:41:23.527  INFO 448872 --- [           main] c.s.c.b.l.i.LoadApiFromAnnoListener      : anno load model num is 107.
2025-07-30 22:41:23.527  INFO 448872 --- [           main] c.s.c.b.l.i.LoadApiFromAnnoListener      : ApiListener end.
2025-07-30 22:41:23.527  INFO 448872 --- [           main] c.s.c.b.l.SkyeyeStartListener            : loadApiFromAnnoListener executor end.
2025-07-30 22:41:23.527  INFO 448872 --- [           main] c.s.c.b.l.SkyeyeStartListener            : loadClassEnumListener start.
2025-07-30 22:41:23.527  INFO 448872 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read skyeye enum start.
2025-07-30 22:41:23.527  INFO 448872 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : read enum basePackage is com.skyeye.
2025-07-30 22:41:23.563  INFO 448872 --- [           main] c.s.c.b.l.i.LoadClassEnumListener        : current enum is com.skyeye.dsform.classenum.DateTimeType.
2025-07-30 22:41:23.566  INFO 448872 --- [           main] ConditionEvaluationReportLoggingListener :

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-30 22:41:23.567  INFO 448872 --- [           main] o.q.c.QuartzScheduler                    : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 22:41:25.678  INFO 448872 --- [lientSelector_1] RocketmqRemoting                         : closeChannel: close the connection to remote address[**************:10911] result: true
2025-07-30 22:41:25.724  INFO 448872 --- [lientSelector_1] RocketmqRemoting                         : closeChannel: close the connection to remote address[**************:10911] result: true
2025-07-30 22:41:25.773  INFO 448872 --- [lientSelector_1] RocketmqRemoting                         : closeChannel: close the connection to remote address[**************:10911] result: true
2025-07-30 22:41:28.679  INFO 448872 --- [lientSelector_1] RocketmqRemoting                         : closeChannel: close the connection to remote address[**************:10911] result: true
2025-07-30 22:41:28.679  INFO 448872 --- [lientSelector_1] RocketmqRemoting                         : closeChannel: close the connection to remote address[*************:9876] result: true
2025-07-30 22:41:31.680  INFO 448872 --- [lientSelector_1] RocketmqRemoting                         : closeChannel: close the connection to remote address[**************:10911] result: true
2025-07-30 22:41:31.680  INFO 448872 --- [lientSelector_1] RocketmqRemoting                         : closeChannel: close the connection to remote address[*************:9876] result: true
2025-07-30 22:41:37.683  INFO 448872 --- [lientSelector_1] RocketmqRemoting                         : closeChannel: close the connection to remote address[*************:9876] result: true
2025-07-30 22:41:37.683  INFO 448872 --- [lientSelector_1] RocketmqRemoting                         : closeChannel: close the connection to remote address[**************:10911] result: true
2025-07-30 22:41:37.693  INFO 448872 --- [           main] c.s.c.f.SessionFilter                    : system is destory!!!!
2025-07-30 22:41:37.695  INFO 448872 --- [           main] a.r.s.s.DefaultRocketMQListenerContainer : container destroyed, DefaultRocketMQListenerContainer{consumerGroup='SMS_SEND_SERVICE', nameServer='*************:9876', topic='SMS_SEND_SERVICE', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='dev', messageModel=CLUSTERING}
2025-07-30 22:41:37.695  INFO 448872 --- [           main] a.r.s.s.DefaultRocketMQListenerContainer : container destroyed, DefaultRocketMQListenerContainer{consumerGroup='ORDINARY_MAIL_DELIVERY_SERVICE', nameServer='*************:9876', topic='ORDINARY_MAIL_DELIVERY_SERVICE', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='dev', messageModel=CLUSTERING}
2025-07-30 22:41:37.696  INFO 448872 --- [           main] a.r.s.s.DefaultRocketMQListenerContainer : container destroyed, DefaultRocketMQListenerContainer{consumerGroup='NOTICE_SEND_SERVICE', nameServer='*************:9876', topic='NOTICE_SEND_SERVICE', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='dev', messageModel=CLUSTERING}
2025-07-30 22:41:37.700  INFO 448872 --- [           main] o.s.s.q.SchedulerFactoryBean             : Shutting down Quartz Scheduler
2025-07-30 22:41:37.700  INFO 448872 --- [           main] o.q.c.QuartzScheduler                    : Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-30 22:41:37.700  INFO 448872 --- [           main] o.q.c.QuartzScheduler                    : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-30 22:41:37.700  INFO 448872 --- [           main] o.q.c.QuartzScheduler                    : Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-30 22:41:37.701  INFO 448872 --- [           main] c.a.c.n.r.NacosServiceRegistry           : De-registering from Nacos Server now...
2025-07-30 22:41:37.707  INFO 448872 --- [           main] c.a.c.n.r.NacosServiceRegistry           : De-registration finished.
2025-07-30 22:41:37.712  INFO 448872 --- [      Thread-31] c.x.j.c.s.EmbedServer                    : >>>>>>>>>>> xxl-job remoting server stop.
2025-07-30 22:41:37.715  INFO 448872 --- [rRegistryThread] c.x.j.c.t.ExecutorRegistryThread         : >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='xxl-job-skyeye-pro-dev-executor', registryValue='http://26.44.187.191:18081/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-07-30 22:41:37.715  INFO 448872 --- [rRegistryThread] c.x.j.c.t.ExecutorRegistryThread         : >>>>>>>>>>> xxl-job, executor registry thread destory.
2025-07-30 22:41:37.715  INFO 448872 --- [           main] c.x.j.c.s.EmbedServer                    : >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-30 22:41:37.716  INFO 448872 --- [FileCleanThread] c.x.j.c.t.JobLogFileCleanThread          : >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destory.
2025-07-30 22:41:37.716  INFO 448872 --- [rCallbackThread] c.x.j.c.t.TriggerCallbackThread          : >>>>>>>>>>> xxl-job, executor callback thread destory.
2025-07-30 22:41:37.716  INFO 448872 --- [      Thread-30] c.x.j.c.t.TriggerCallbackThread          : >>>>>>>>>>> xxl-job, executor retry callback thread destory.
2025-07-30 22:41:43.719  INFO 448872 --- [lientSelector_1] RocketmqRemoting                         : closeChannel: close the connection to remote address[**************:10911] result: true
2025-07-30 22:41:43.719  INFO 448872 --- [lientSelector_1] RocketmqRemoting                         : closeChannel: close the connection to remote address[*************:9876] result: true
2025-07-30 22:41:43.786  WARN 448872 --- [           main] s.c.a.AnnotationConfigApplicationContext : Exception thrown from ApplicationListener handling ContextClosedEvent

org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'registrationListener': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1168) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.getTargetBean(ApplicationListenerMethodAdapter.java:371) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:336) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:229) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:166) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:435) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:99) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1120) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1086) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:94) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:82) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.bootstrap.BootstrapApplicationListener$CloseContextOnFailureApplicationListener.onApplicationEvent(BootstrapApplicationListener.java:504) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.context.event.EventPublishingRunListener.failed(EventPublishingRunListener.java:124) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.callFailedListener(SpringApplicationRunListeners.java:96) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.lambda$failed$7(SpringApplicationRunListeners.java:87) ~[spring-boot-2.7.18.jar:2.7.18]
	at java.util.ArrayList.forEach(ArrayList.java:1511) [?:?]
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:120) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.failed(SpringApplicationRunListeners.java:86) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.handleRunFailure(SpringApplication.java:778) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289) [spring-boot-2.7.18.jar:2.7.18]
	at com.SkyEyeApplication.main(SkyEyeApplication.java:39) [classes/:?]

2025-07-30 22:41:43.790  WARN 448872 --- [           main] s.c.a.AnnotationConfigApplicationContext : Exception thrown from ApplicationListener handling ContextClosedEvent

org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'registrationListener': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1168) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.getTargetBean(ApplicationListenerMethodAdapter.java:371) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:336) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:229) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:166) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:435) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:99) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1120) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1086) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:94) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:82) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.bootstrap.BootstrapApplicationListener$CloseContextOnFailureApplicationListener.onApplicationEvent(BootstrapApplicationListener.java:504) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.context.event.EventPublishingRunListener.failed(EventPublishingRunListener.java:124) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.callFailedListener(SpringApplicationRunListeners.java:96) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.lambda$failed$7(SpringApplicationRunListeners.java:87) ~[spring-boot-2.7.18.jar:2.7.18]
	at java.util.ArrayList.forEach(ArrayList.java:1511) [?:?]
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:120) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.failed(SpringApplicationRunListeners.java:86) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.handleRunFailure(SpringApplication.java:778) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289) [spring-boot-2.7.18.jar:2.7.18]
	at com.SkyEyeApplication.main(SkyEyeApplication.java:39) [classes/:?]

2025-07-30 22:41:43.791  WARN 448872 --- [           main] s.c.a.AnnotationConfigApplicationContext : Exception thrown from ApplicationListener handling ContextClosedEvent

org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'registrationListener': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1168) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.getTargetBean(ApplicationListenerMethodAdapter.java:371) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:336) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:229) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:166) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:435) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:99) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1120) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1086) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:94) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:82) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.bootstrap.BootstrapApplicationListener$CloseContextOnFailureApplicationListener.onApplicationEvent(BootstrapApplicationListener.java:504) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.context.event.EventPublishingRunListener.failed(EventPublishingRunListener.java:124) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.callFailedListener(SpringApplicationRunListeners.java:96) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.lambda$failed$7(SpringApplicationRunListeners.java:87) ~[spring-boot-2.7.18.jar:2.7.18]
	at java.util.ArrayList.forEach(ArrayList.java:1511) [?:?]
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:120) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.failed(SpringApplicationRunListeners.java:86) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.handleRunFailure(SpringApplication.java:778) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289) [spring-boot-2.7.18.jar:2.7.18]
	at com.SkyEyeApplication.main(SkyEyeApplication.java:39) [classes/:?]

2025-07-30 22:41:43.791  WARN 448872 --- [           main] s.c.a.AnnotationConfigApplicationContext : Exception thrown from ApplicationListener handling ContextClosedEvent

org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'registrationListener': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1168) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.getTargetBean(ApplicationListenerMethodAdapter.java:371) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:336) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:229) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:166) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:435) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:99) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1120) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1086) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:94) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:82) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.bootstrap.BootstrapApplicationListener$CloseContextOnFailureApplicationListener.onApplicationEvent(BootstrapApplicationListener.java:504) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.context.event.EventPublishingRunListener.failed(EventPublishingRunListener.java:124) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.callFailedListener(SpringApplicationRunListeners.java:96) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.lambda$failed$7(SpringApplicationRunListeners.java:87) ~[spring-boot-2.7.18.jar:2.7.18]
	at java.util.ArrayList.forEach(ArrayList.java:1511) [?:?]
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:120) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.failed(SpringApplicationRunListeners.java:86) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.handleRunFailure(SpringApplication.java:778) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289) [spring-boot-2.7.18.jar:2.7.18]
	at com.SkyEyeApplication.main(SkyEyeApplication.java:39) [classes/:?]

2025-07-30 22:41:43.791  WARN 448872 --- [           main] s.c.a.AnnotationConfigApplicationContext : Exception thrown from ApplicationListener handling ContextClosedEvent

org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'registrationListener': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1168) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.getTargetBean(ApplicationListenerMethodAdapter.java:371) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:336) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:229) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:166) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:435) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:99) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1120) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1086) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:94) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:82) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.bootstrap.BootstrapApplicationListener$CloseContextOnFailureApplicationListener.onApplicationEvent(BootstrapApplicationListener.java:504) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.context.event.EventPublishingRunListener.failed(EventPublishingRunListener.java:124) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.callFailedListener(SpringApplicationRunListeners.java:96) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.lambda$failed$7(SpringApplicationRunListeners.java:87) ~[spring-boot-2.7.18.jar:2.7.18]
	at java.util.ArrayList.forEach(ArrayList.java:1511) [?:?]
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:120) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.failed(SpringApplicationRunListeners.java:86) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.handleRunFailure(SpringApplication.java:778) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289) [spring-boot-2.7.18.jar:2.7.18]
	at com.SkyEyeApplication.main(SkyEyeApplication.java:39) [classes/:?]

2025-07-30 22:41:43.791  WARN 448872 --- [           main] s.c.a.AnnotationConfigApplicationContext : Exception thrown from ApplicationListener handling ContextClosedEvent

org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'registrationListener': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1168) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.getTargetBean(ApplicationListenerMethodAdapter.java:371) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:336) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:229) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:166) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:435) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:99) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1120) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1086) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:94) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:82) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.bootstrap.BootstrapApplicationListener$CloseContextOnFailureApplicationListener.onApplicationEvent(BootstrapApplicationListener.java:504) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.context.event.EventPublishingRunListener.failed(EventPublishingRunListener.java:124) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.callFailedListener(SpringApplicationRunListeners.java:96) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.lambda$failed$7(SpringApplicationRunListeners.java:87) ~[spring-boot-2.7.18.jar:2.7.18]
	at java.util.ArrayList.forEach(ArrayList.java:1511) [?:?]
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:120) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.failed(SpringApplicationRunListeners.java:86) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.handleRunFailure(SpringApplication.java:778) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289) [spring-boot-2.7.18.jar:2.7.18]
	at com.SkyEyeApplication.main(SkyEyeApplication.java:39) [classes/:?]

2025-07-30 22:41:43.791  WARN 448872 --- [           main] s.c.a.AnnotationConfigApplicationContext : Exception thrown from ApplicationListener handling ContextClosedEvent

org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'registrationListener': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1168) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.getTargetBean(ApplicationListenerMethodAdapter.java:371) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:336) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:229) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:166) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:435) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:99) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1120) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1086) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:94) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:82) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.bootstrap.BootstrapApplicationListener$CloseContextOnFailureApplicationListener.onApplicationEvent(BootstrapApplicationListener.java:504) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.context.event.EventPublishingRunListener.failed(EventPublishingRunListener.java:124) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.callFailedListener(SpringApplicationRunListeners.java:96) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.lambda$failed$7(SpringApplicationRunListeners.java:87) ~[spring-boot-2.7.18.jar:2.7.18]
	at java.util.ArrayList.forEach(ArrayList.java:1511) [?:?]
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:120) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.failed(SpringApplicationRunListeners.java:86) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.handleRunFailure(SpringApplication.java:778) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289) [spring-boot-2.7.18.jar:2.7.18]
	at com.SkyEyeApplication.main(SkyEyeApplication.java:39) [classes/:?]

2025-07-30 22:41:43.792  WARN 448872 --- [           main] s.c.a.AnnotationConfigApplicationContext : Exception thrown from ApplicationListener handling ContextClosedEvent

org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'registrationListener': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1168) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.getTargetBean(ApplicationListenerMethodAdapter.java:371) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:336) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:229) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:166) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:435) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:99) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1120) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1086) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:94) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:82) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.bootstrap.BootstrapApplicationListener$CloseContextOnFailureApplicationListener.onApplicationEvent(BootstrapApplicationListener.java:504) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.context.event.EventPublishingRunListener.failed(EventPublishingRunListener.java:124) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.callFailedListener(SpringApplicationRunListeners.java:96) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.lambda$failed$7(SpringApplicationRunListeners.java:87) ~[spring-boot-2.7.18.jar:2.7.18]
	at java.util.ArrayList.forEach(ArrayList.java:1511) [?:?]
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:120) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.failed(SpringApplicationRunListeners.java:86) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.handleRunFailure(SpringApplication.java:778) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289) [spring-boot-2.7.18.jar:2.7.18]
	at com.SkyEyeApplication.main(SkyEyeApplication.java:39) [classes/:?]

2025-07-30 22:41:43.792  WARN 448872 --- [           main] s.c.a.AnnotationConfigApplicationContext : Exception thrown from ApplicationListener handling ContextClosedEvent

org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'registrationListener': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1168) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.getTargetBean(ApplicationListenerMethodAdapter.java:371) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:336) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:229) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:166) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:435) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:99) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1120) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1086) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:94) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:82) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.bootstrap.BootstrapApplicationListener$CloseContextOnFailureApplicationListener.onApplicationEvent(BootstrapApplicationListener.java:504) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.context.event.EventPublishingRunListener.failed(EventPublishingRunListener.java:124) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.callFailedListener(SpringApplicationRunListeners.java:96) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.lambda$failed$7(SpringApplicationRunListeners.java:87) ~[spring-boot-2.7.18.jar:2.7.18]
	at java.util.ArrayList.forEach(ArrayList.java:1511) [?:?]
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:120) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.failed(SpringApplicationRunListeners.java:86) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.handleRunFailure(SpringApplication.java:778) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289) [spring-boot-2.7.18.jar:2.7.18]
	at com.SkyEyeApplication.main(SkyEyeApplication.java:39) [classes/:?]

2025-07-30 22:41:43.792  WARN 448872 --- [           main] s.c.a.AnnotationConfigApplicationContext : Exception thrown from ApplicationListener handling ContextClosedEvent

org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'registrationListener': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1168) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.getTargetBean(ApplicationListenerMethodAdapter.java:371) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:336) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:229) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:166) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:435) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:99) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1120) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1086) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:94) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:82) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.bootstrap.BootstrapApplicationListener$CloseContextOnFailureApplicationListener.onApplicationEvent(BootstrapApplicationListener.java:504) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.context.event.EventPublishingRunListener.failed(EventPublishingRunListener.java:124) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.callFailedListener(SpringApplicationRunListeners.java:96) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.lambda$failed$7(SpringApplicationRunListeners.java:87) ~[spring-boot-2.7.18.jar:2.7.18]
	at java.util.ArrayList.forEach(ArrayList.java:1511) [?:?]
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:120) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.failed(SpringApplicationRunListeners.java:86) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.handleRunFailure(SpringApplication.java:778) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289) [spring-boot-2.7.18.jar:2.7.18]
	at com.SkyEyeApplication.main(SkyEyeApplication.java:39) [classes/:?]

2025-07-30 22:41:43.792  WARN 448872 --- [           main] s.c.a.AnnotationConfigApplicationContext : Exception thrown from ApplicationListener handling ContextClosedEvent

org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'registrationListener': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1168) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.getTargetBean(ApplicationListenerMethodAdapter.java:371) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:336) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:229) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:166) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:435) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:99) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1120) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1086) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:94) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:82) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.bootstrap.BootstrapApplicationListener$CloseContextOnFailureApplicationListener.onApplicationEvent(BootstrapApplicationListener.java:504) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.context.event.EventPublishingRunListener.failed(EventPublishingRunListener.java:124) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.callFailedListener(SpringApplicationRunListeners.java:96) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.lambda$failed$7(SpringApplicationRunListeners.java:87) ~[spring-boot-2.7.18.jar:2.7.18]
	at java.util.ArrayList.forEach(ArrayList.java:1511) [?:?]
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:120) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.failed(SpringApplicationRunListeners.java:86) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.handleRunFailure(SpringApplication.java:778) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289) [spring-boot-2.7.18.jar:2.7.18]
	at com.SkyEyeApplication.main(SkyEyeApplication.java:39) [classes/:?]

2025-07-30 22:41:43.792  WARN 448872 --- [           main] s.c.a.AnnotationConfigApplicationContext : Exception thrown from ApplicationListener handling ContextClosedEvent

org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'registrationListener': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1168) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.getTargetBean(ApplicationListenerMethodAdapter.java:371) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:336) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:229) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:166) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:435) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:99) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1120) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1086) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:94) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:82) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.bootstrap.BootstrapApplicationListener$CloseContextOnFailureApplicationListener.onApplicationEvent(BootstrapApplicationListener.java:504) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.context.event.EventPublishingRunListener.failed(EventPublishingRunListener.java:124) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.callFailedListener(SpringApplicationRunListeners.java:96) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.lambda$failed$7(SpringApplicationRunListeners.java:87) ~[spring-boot-2.7.18.jar:2.7.18]
	at java.util.ArrayList.forEach(ArrayList.java:1511) [?:?]
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:120) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.failed(SpringApplicationRunListeners.java:86) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.handleRunFailure(SpringApplication.java:778) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289) [spring-boot-2.7.18.jar:2.7.18]
	at com.SkyEyeApplication.main(SkyEyeApplication.java:39) [classes/:?]

2025-07-30 22:41:43.793  WARN 448872 --- [           main] s.c.a.AnnotationConfigApplicationContext : Exception thrown from ApplicationListener handling ContextClosedEvent

org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'registrationListener': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1168) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.getTargetBean(ApplicationListenerMethodAdapter.java:371) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:336) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:229) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:166) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:435) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:99) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1120) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1086) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:94) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.ParentContextCloserApplicationListener$ContextCloserListener.onApplicationEvent(ParentContextCloserApplicationListener.java:82) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.bootstrap.BootstrapApplicationListener$CloseContextOnFailureApplicationListener.onApplicationEvent(BootstrapApplicationListener.java:504) ~[spring-cloud-context-3.1.8.jar:3.1.8]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.context.event.EventPublishingRunListener.failed(EventPublishingRunListener.java:124) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.callFailedListener(SpringApplicationRunListeners.java:96) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.lambda$failed$7(SpringApplicationRunListeners.java:87) ~[spring-boot-2.7.18.jar:2.7.18]
	at java.util.ArrayList.forEach(ArrayList.java:1511) [?:?]
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:120) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.failed(SpringApplicationRunListeners.java:86) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.handleRunFailure(SpringApplication.java:778) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289) [spring-boot-2.7.18.jar:2.7.18]
	at com.SkyEyeApplication.main(SkyEyeApplication.java:39) [classes/:?]

2025-07-30 22:41:43.795  INFO 448872 --- [           main] c.z.h.HikariDataSource                   : HikariPool-1 - Shutdown initiated...
2025-07-30 22:41:43.800  INFO 448872 --- [           main] c.z.h.HikariDataSource                   : HikariPool-1 - Shutdown completed.
2025-07-30 22:41:43.819 ERROR 448872 --- [           main] o.s.b.SpringApplication                  : Application run failed

java.lang.IllegalStateException: Failed to execute ApplicationRunner
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:759) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746) [spring-boot-2.7.18.jar:2.7.18]
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183) ~[?:?]
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:357) ~[?:?]
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:510) ~[?:?]
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499) ~[?:?]
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150) ~[?:?]
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173) ~[?:?]
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234) ~[?:?]
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596) ~[?:?]
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289) [spring-boot-2.7.18.jar:2.7.18]
	at com.SkyEyeApplication.main(SkyEyeApplication.java:39) [classes/:?]
Caused by: java.lang.reflect.InvocationTargetException
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at com.skyeye.common.base.listener.SkyeyeStartListener.run(SkyeyeStartListener.java:62) ~[classes/:?]
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756) ~[spring-boot-2.7.18.jar:2.7.18]
	... 14 more
Caused by: java.lang.reflect.InaccessibleObjectException: Unable to make field private final java.lang.String java.lang.Enum.name accessible: module java.base does not "opens java.lang" to unnamed module @1e6a3214
	at java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:354) ~[?:?]
	at java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:297) ~[?:?]
	at java.lang.reflect.Field.checkCanSetAccessible(Field.java:178) ~[?:?]
	at java.lang.reflect.Field.setAccessible(Field.java:172) ~[?:?]
	at cn.hutool.core.util.ReflectUtil.setAccessible(ReflectUtil.java:1122) ~[hutool-all-5.8.39.jar:5.8.39]
	at cn.hutool.core.util.ReflectUtil.getFieldValue(ReflectUtil.java:271) ~[hutool-all-5.8.39.jar:5.8.39]
	at cn.hutool.core.util.ReflectUtil.getFieldValue(ReflectUtil.java:239) ~[hutool-all-5.8.39.jar:5.8.39]
	at com.skyeye.common.base.listener.impl.LoadClassEnumListener.getEnumDataList(LoadClassEnumListener.java:99) ~[classes/:?]
	at com.skyeye.common.base.listener.impl.LoadClassEnumListener.run(LoadClassEnumListener.java:64) ~[classes/:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:569) ~[?:?]
	at com.skyeye.common.base.listener.SkyeyeStartListener.run(SkyeyeStartListener.java:62) ~[classes/:?]
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756) ~[spring-boot-2.7.18.jar:2.7.18]
	... 14 more

