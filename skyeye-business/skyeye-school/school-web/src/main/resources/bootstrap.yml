server:
  port: 8084

spring:
  application:
    name: skyeye-school-${spring.profiles.active} # 服务名
  main:
    allow-circular-references: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。
    allow-bean-definition-overriding: true # 允许 Bean 覆盖，例如说 Feign 等会存在重复定义的服务
  data:
    redis:
      repositories:
        enabled: false # 项目未使用到 Spring Data Redis 的 Repository，所以直接禁用，保证启动速度
  profiles:
    active: dev
  cloud:
    nacos:
      discovery:
        server-addr: 192.168.31.71:8848 # 配置服务注册nacos地址
        namespace: ${spring.profiles.active} # 配置命名空间
      config:
        # 指定nacos server的地址
        server-addr: 192.168.31.71:8848
        # 配置文件后缀
        file-extension: yml
        # 命名空间 常用场景之一是不同环境的配置的区分隔离，例如开发测试环境和生产环境的资源（如配置、服务）隔离等
        namespace: ${spring.profiles.active} # 配置命名空间
        # 支持多个共享 Datum Id 的配置，优先级小于ext-config,自定义 Datum Id 配置 属性是个集合，内部由 Config POJO 组成。Config 有 3 个属性，分别是 dataId, group 以及 refresh
        ext-config:
          - data-id: skyeye-common.yml # 配置文件名-Datum Id
            group: DEFAULT_GROUP # 默认为DEFAULT_GROUP
            refresh: false # 是否动态刷新，默认为false
logging:
  level:
    com.skyeye: debug
#    com.skyeye.eve.dao: debug
feign:
  client:
    config:
      # 全局配置
      default:
        loggerLevel: FULL

webroot:
  # 表白墙服务
  skyeye-wall: skyeye-wall-${spring.profiles.active}
  # 基础服务
  skyeye-pro: skyeye-pro-${spring.profiles.active}
